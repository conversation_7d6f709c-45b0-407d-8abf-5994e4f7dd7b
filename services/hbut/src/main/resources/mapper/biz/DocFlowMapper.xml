<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ahead4.cdes.mapper.DocFlowMapper">

    <select id="ccPage" resultType="org.ahead4.cdes.entity.DocFlow">
        select distinct d.*
        from wf_copy c
        inner join hb_doc_flow d on c.business_key = d.id
        <where> d.del_flag = '0' and c.user_id = #{username}
        <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
            AND ${ew.customSqlSegment.substring(5)}
        </if>
        </where>
        order by c.update_time desc

    </select>

    <select id="finishedList" resultType="org.ahead4.cdes.entity.DocFlow">
        select  id, flow_code, doc_title, doc_type, priority_level, is_remind, doc_desp,
         is_public, make_copy, del_flag, creator, create_time, updator, update_time,
        proc_inst_id, "status"
        from hb_doc_flow
        where proc_inst_id in (
            select proc_inst_id_ from ACT_HI_TASKINST
            where ASSIGNEE_ = #{username}
              and END_TIME_ is not null
            group by proc_inst_id_
        )
        and del_flag = '0'
        <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
             <choose>
                <when test="ew.customSqlSegment.trim().startsWith('where') || ew.customSqlSegment.trim().startsWith('WHERE')">
                    AND  ${ew.customSqlSegment.substring(5)}
                </when>
                <otherwise>
                    ${ew.customSqlSegment}
                </otherwise>
            </choose>
        </if>
    </select>

    <resultMap id="WfTaskVoResultMap" type="org.ahead4.workflow.domain.vo.WfTaskVo">
        <result property="taskId" column="taskId"/>
        <result property="taskName" column="taskName"/>
        <result property="taskDefKey" column="taskDefKey"/>
        <result property="assigneeId" column="assignee"/>
        <result property="procInsId" column="procInsId"/>
        <result property="procInsId" column=""/>
        <result property="createTime" column="createTime"/>
        <result property="procVars" column="procVars" typeHandler="org.ahead4.cdes.typehandler.ProcVarsTypeHandler"/>
    </resultMap>

    <select id="todoPageList" resultMap="WfTaskVoResultMap">
        SELECT *
        FROM (
        SELECT T1.ID_ as "taskId",
        T1.NAME_ as "taskName",
        T1.task_def_key_ as "taskDefKey",
        T1.assignee_ as "assignee",
        T1.CREATE_TIME_ as "createTime",
        E_ROOT.BUSINESS_KEY_ as "businessKey",
        E_ROOT.root_proc_inst_id_ as "parentProcessInstanceId",
        T1.PROC_INST_ID_ as "procInsId",
        1 AS "subTaskCount",
        -- 从业务表中选择需要的字段
        BIZ.creator as "creator",
        BIZ.doc_title as "docTitle",
        BIZ.flow_code as "flowCode",
        BIZ.doc_type as "docType",
        BIZ.creator as "initiator",
        BIZ.id AS "id",
        BIZ.proc_inst_id as "procInstId",
        BIZ.status as "status",
        BIZ.priority_level as "priorityLevel",
        BIZ.is_public as "isPublic",
        BIZ.is_remind as "isRemind",
        '' as procVars
        FROM ACT_RU_TASK AS T1
        INNER JOIN
        ACT_RU_EXECUTION AS E_ROOT
        ON T1.PROC_INST_ID_ = E_ROOT.PROC_INST_ID_ AND E_ROOT.PARENT_ID_ IS NULL
        -- 关联业务表
        INNER JOIN
        public.hb_doc_flow AS BIZ ON E_ROOT.PROC_INST_ID_ = BIZ.proc_inst_id
        WHERE T1.ASSIGNEE_ = #{username}
        AND E_ROOT.SUPER_EXEC_ IS NULL
        <if test="query.params.flowCodeLike != null and query.params.flowCodeLike != ''">
            AND BIZ.flow_code = #{query.params.flowCodeLike}
        </if>
        <if test="query.params.docTitleLike != null and query.params.docTitleLike != ''">
            AND BIZ.doc_title LIKE #{query.params.docTitleLike}
        </if>
        <if test="query.params.status != null and query.params.status != ''">
            and BIZ.status = #{query.params.status}
        </if>
        <if test="query.params.docType != null and query.params.docType != ''">
            and BIZ.doc_type = #{query.params.docType}
        </if>
        <if test="query.params.startTime != null and query.params.startTime != ''">
            and BIZ.create_time &gt;= #{query.params.startTime}
        </if>
        <if test="query.params.endTime != null and query.params.endTime != ''">
            and BIZ.create_time &lt;= #{query.params.endTime}
        </if>
        AND BIZ.del_flag = '0'

        UNION ALL

        SELECT DISTINCT
        ON (E_MAIN_ROOT.BUSINESS_KEY_)
        T1.ID_ as "taskId",
        T1.NAME_ as "taskName",
        T1.task_def_key_ as "taskDefKey",
        T1.assignee_ as "assignee",
        T1.CREATE_TIME_ as "createTime",
        E_MAIN_ROOT.BUSINESS_KEY_ as "businessKey",
        E_MAIN_ROOT.root_proc_inst_id_ as "parentProcessInstanceId",
        E_MAIN_ROOT.PROC_INST_ID_ as "procInsId",
        COUNT (T1.ID_) OVER (PARTITION BY E_MAIN_ROOT.BUSINESS_KEY_) as "subTaskCount",
        -- 从业务表中选择需要的字段
        BIZ.creator as "creator",
        BIZ.doc_title as "docTitle",
        BIZ.flow_code as "flowCode",
        BIZ.doc_type as "docType",
        BIZ.creator as "initiator",
        BIZ.id AS "id",
        BIZ.proc_inst_id as "procInstId",
        BIZ.status as "status",
        BIZ.priority_level as "priorityLevel",
        BIZ.is_public as "isPublic",
        BIZ.is_remind as "isRemind",
        '' as procVars
        FROM
            ACT_RU_TASK AS T1
            INNER JOIN
            ACT_RU_EXECUTION AS E_SUB
        ON T1.PROC_INST_ID_ = E_SUB.PROC_INST_ID_ AND E_SUB.PARENT_ID_ IS NULL
            INNER JOIN
        ACT_RU_EXECUTION AS E_MAIN_CALL ON E_SUB.SUPER_EXEC_ = E_MAIN_CALL.ID_
            INNER JOIN
        ACT_RU_EXECUTION AS E_MAIN_ROOT ON E_MAIN_CALL.PROC_INST_ID_ = E_MAIN_ROOT.PROC_INST_ID_ AND
        E_MAIN_ROOT.PARENT_ID_ IS NULL
        -- 关联业务表
            INNER JOIN
        public.hb_doc_flow AS BIZ ON E_MAIN_ROOT.PROC_INST_ID_ = BIZ.proc_inst_id
        WHERE
        T1.ASSIGNEE_ = #{username}
        AND E_SUB.SUPER_EXEC_ IS NOT NULL
          AND E_MAIN_ROOT.BUSINESS_KEY_ IS NOT NULL
        -- 动态业务查询条件
        <if test="query.params.flowCodeLike != null and query.params.flowCodeLike != ''">
            AND BIZ.flow_code = #{query.params.flowCodeLike}
        </if>
        <if test="query.params.docTitleLike != null and query.params.docTitleLike != ''">
            AND BIZ.doc_title LIKE #{query.params.docTitleLike}
        </if>
        <if test="query.params.status != null and query.params.status != ''">
            and BIZ.status = #{query.params.status}
        </if>
        <if test="query.params.docType != null and query.params.docType != ''">
            and BIZ.doc_type = #{query.params.docType}
        </if>
        <if test="query.params.startTime != null and query.params.startTime != ''">
            and BIZ.create_time &gt;= #{query.params.startTime}
        </if>
        <if test="query.params.endTime != null and query.params.endTime != ''">
            and BIZ.create_time &lt;= #{query.params.endTime}
        </if>
        ) AS all_tasks
        ORDER BY "createTime" DESC
    </select>

    <select id="finishedListV2" resultType="org.ahead4.cdes.entity.DocFlow">
        -- 1. 定义递归 CTE, 用于查找每个流程实例的最终根实例
        WITH RECURSIVE process_ancestry (start_id, parent_id, root_id) AS (
            -- 锚点: 所有流程实例，初始时 start_id 和 root_id 都是它自己
            SELECT
                id_ as start_id,
                super_process_instance_id_ as parent_id,
                id_ as root_id
            FROM
                public.act_hi_procinst

            UNION ALL
            -- 递归: 向上查找父级，并用父级的 ID 更新 root_id
            SELECT
                pa.start_id,
                p.super_process_instance_id_,
                p.id_
            FROM
                process_ancestry pa
                    JOIN
                public.act_hi_procinst p ON pa.parent_id = p.id_
        ),
-- 2. 构建一个清晰的 "任意流程ID -> 根流程ID" 的最终映射表
                       proc_to_root_map AS (
                           SELECT start_id, root_id FROM process_ancestry WHERE parent_id IS NULL
                       )
-- 3. 主查询
        SELECT
            create_time as "createTime",
            creator as "creator",
            del_flag as "delFlag",
            doc_desp as "docDesp",
            doc_title as "docTitle",
            doc_type as "docType",
            flow_code as "flowCode",
            id as "Id",
            is_public as "isPublic",
            is_remind as "isRemind",
            priority_level as "priorityLevel",
            proc_inst_id as "processInstanceId",
            status as "status",
            updator as "updator",
            update_time as "updateTime",
            involved_procs.last_involved_time as "lastInvolvedTime"
        FROM
            public.hb_doc_flow
                INNER JOIN
            (
                SELECT
                    MAP.root_id,
                    MAX(HI_T.END_TIME_) as last_involved_time
                FROM
                    public.act_hi_taskinst AS HI_T
                        INNER JOIN
                    proc_to_root_map AS MAP ON HI_T.PROC_INST_ID_ = MAP.start_id
                WHERE
                    HI_T.ASSIGNEE_ = #{username}
                  AND HI_T.END_TIME_ IS NOT NULL
                GROUP BY
                    MAP.root_id
            ) AS involved_procs ON proc_inst_id = involved_procs.root_id
        WHERE
            del_flag = '0'
        <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
            <choose>
                <when test="ew.customSqlSegment.trim().startsWith('where') || ew.customSqlSegment.trim().startsWith('WHERE')">
                    AND  ${ew.customSqlSegment.substring(5)}
                </when>
                <otherwise>
                    ${ew.customSqlSegment}
                </otherwise>
            </choose>
        </if>
        ORDER BY
            involved_procs.last_involved_time DESC
    </select>
</mapper>