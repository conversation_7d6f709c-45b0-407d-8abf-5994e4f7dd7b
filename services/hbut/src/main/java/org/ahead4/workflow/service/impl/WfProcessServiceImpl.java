package org.ahead4.workflow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.cdes.entity.dto.FlowComment;
import org.ahead4.web.exception.RestException;
import org.ahead4.workflow.constant.ProcessConstants;
import org.ahead4.workflow.constant.TaskConstants;
import org.ahead4.workflow.core.FormConf;
import org.ahead4.workflow.core.domain.ProcessQuery;
import org.ahead4.workflow.domain.WfDeployForm;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.*;
import org.ahead4.workflow.domain.wrapper.MergedHistoricTaskInstance;
import org.ahead4.workflow.enums.ProcessStatus;
import org.ahead4.workflow.factory.FlowServiceFactory;
import org.ahead4.workflow.flow.FlowableUtils;
import org.ahead4.workflow.mapper.WfDeployFormMapper;
import org.ahead4.workflow.service.IWfProcessService;
import org.ahead4.workflow.service.IWfTaskService;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.ahead4.workflow.utils.DateUtils;
import org.ahead4.workflow.utils.JsonUtils;
import org.ahead4.workflow.utils.ModelUtils;
import org.ahead4.workflow.utils.ProcessFormUtils;
import org.ahead4.workflow.utils.ProcessUtils;
import org.ahead4.workflow.utils.StringUtils;
import org.ahead4.workflow.utils.TaskUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.groovy.util.Maps;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.CallActivity;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.MultiInstanceLoopCharacteristics;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.SubProcess;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 2022/3/24 18:57
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WfProcessServiceImpl extends FlowServiceFactory implements IWfProcessService {

    private final IWfTaskService wfTaskService;
    private final BizSystemAdapter adapter;
    private final WfDeployFormMapper deployFormMapper;

    /**
     * 多实例批次识别时间窗口（毫秒）
     * 在此时间窗口内创建的任务被认为属于同一批次
     */
    private static final long MULTI_INSTANCE_BATCH_TIME_WINDOW = 5 * 60 * 1000L; // 5分钟

    /**
     * 审批轮次间隔阈值（毫秒）
     * 超过此时间间隔的同一节点任务被认为是不同的审批轮次（如驳回重审）
     */
    private static final long APPROVAL_ROUND_INTERVAL_THRESHOLD = 60 * 60 * 1000L; // 1小时

    /**
     * 流程定义列表
     *
     * @param pageQuery 分页参数
     * @return 流程定义分页列表数据
     */
    @Override
    public TableDataInfo<WfDefinitionVo> selectPageStartProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfDefinitionVo> page = new Page<>();
        // 流程定义列表数据查询
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery()
            .latestVersion()
            .active()
            .orderByProcessDefinitionKey()
            .asc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(processDefinitionQuery, processQuery);
        long pageTotal = processDefinitionQuery.count();
        if (pageTotal <= 0) {
            return TableDataInfo.build();
        }
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<ProcessDefinition> definitionList = processDefinitionQuery.listPage(offset, pageQuery.getPageSize());

        List<WfDefinitionVo> definitionVoList = new ArrayList<>();
        for (ProcessDefinition processDefinition : definitionList) {
            String deploymentId = processDefinition.getDeploymentId();
            Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
            WfDefinitionVo vo = getWfDefinitionVo(processDefinition, deployment);
            definitionVoList.add(vo);
        }
        page.setRecords(definitionVoList);
        page.setTotal(pageTotal);
        return TableDataInfo.build(page);
    }

    @Override
    public List<WfProcessHistoryVo> getProcessHistoryWithComments(String processInstanceId, String sort) {
        List<HistoricTaskInstance> mainTasks =
            historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).finished().orderByHistoricTaskInstanceEndTime().asc().list();
        List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
        // commentList 按taskId为 key 转换成 map<taskId, Comment>
        Map<String, Comment> commentMap =
            commentList.stream().collect(Collectors.toMap(Comment::getTaskId, comment -> comment));

        // 合并并行多实例任务
        List<HistoricTaskInstance> mergedTasks = mergeParallelMultiInstanceTasks(mainTasks);

        List<WfProcessHistoryVo> historyList =
                mergedTasks.stream().map(task -> this.convertTaskToHistoryVo(task, commentMap))
                        .collect(Collectors.toList());

        Set<String> userIds = mainTasks.stream()
            .map(HistoricTaskInstance::getAssignee)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        List<HistoricProcessInstance> subProcesses = historyService.createHistoricProcessInstanceQuery().superProcessInstanceId(processInstanceId).list();
        if (CollUtil.isNotEmpty(subProcesses)) {
            List<String> subProcessIds = subProcesses.stream().map(HistoricProcessInstance::getId).collect(Collectors.toList());


            // Find the single earliest task among all sub-processes
            HistoricTaskInstance firstSubTask = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceIdIn(subProcessIds)
                .orderByHistoricTaskInstanceStartTime().finished().asc()
                    .listPage(0, 1) // Get only the first one
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (firstSubTask != null) {
                if (StringUtils.isNotBlank(firstSubTask.getAssignee())) {
                    userIds.add(firstSubTask.getAssignee());
                }
                WfProcessHistoryVo subHistoryVo = convertTaskToHistoryVo(firstSubTask, commentMap);
                subHistoryVo.setSubProcess(true);
                subHistoryVo.setSubProcessInstanceId(firstSubTask.getProcessInstanceId());
                HistoricVariableInstance deptLeaderVar = historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(firstSubTask.getProcessInstanceId())
                        .variableName("deptLeader")
                        .singleResult();
                if (deptLeaderVar != null && deptLeaderVar.getValue() != null) {
                    subHistoryVo.setSubProcessDetails(Maps.of("deptLeader", deptLeaderVar.getValue()));
                }
                historyList.add(subHistoryVo);
            }
        }

        // 批量获取用户信息
        if (CollUtil.isNotEmpty(userIds)) {
            Map<String, String> userNames = adapter.getUserNameByIds(new ArrayList<>(userIds));
            if (CollUtil.isNotEmpty(userNames)) {
                historyList.forEach(h -> h.setAssigneeName(userNames.get(h.getAssignee())));
            }
        }

        historyList.sort((h1, h2) -> {
            int result = h1.getStartTime().compareTo(h2.getStartTime());
            return "asc".equalsIgnoreCase(sort) ? result : -result;
        });

        return historyList;
    }

    @Override
    public List<WfSubProcessInstanceSummaryVo> getSubProcessSummaryList(String processInstanceId) {
        List<HistoricProcessInstance> subProcesses = historyService.createHistoricProcessInstanceQuery().superProcessInstanceId(processInstanceId).list();
        if (CollUtil.isEmpty(subProcesses)) {
            return Collections.emptyList();
        }
        List<String> subProcessIds = subProcesses.stream().map(HistoricProcessInstance::getId).collect(Collectors.toList());
        List<WfSubProcessInstanceSummaryVo> summaryList = new ArrayList<>();

        List<HistoricTaskInstance> allSubTasks = historyService.createHistoricTaskInstanceQuery().processInstanceIdIn(subProcessIds).finished().orderByHistoricTaskInstanceEndTime().desc().list();
        Map<String, List<HistoricTaskInstance>> subTasksByInstanceId = allSubTasks.stream().collect(Collectors.groupingBy(HistoricTaskInstance::getProcessInstanceId));

        for (HistoricProcessInstance sub : subProcesses) {
            WfSubProcessInstanceSummaryVo summaryVo = new WfSubProcessInstanceSummaryVo();
            summaryVo.setSubProcessInstanceId(sub.getId());
            HistoricVariableInstance var = historyService.createHistoricVariableInstanceQuery().processInstanceId(sub.getId()).variableName("deptLeader").singleResult();
            if (var != null) {
                summaryVo.setDeptLeader(var.getValue());
            }

            if (subTasksByInstanceId.containsKey(sub.getId()) && !subTasksByInstanceId.get(sub.getId()).isEmpty()) {
                HistoricTaskInstance latestTask = subTasksByInstanceId.get(sub.getId()).get(0);
                summaryVo.setLatestActivityName(latestTask.getName());
                summaryVo.setLatestActivityTime(latestTask.getEndTime());
                List<Comment> comments = taskService.getTaskComments(latestTask.getId());
                if (CollUtil.isNotEmpty(comments)) {
                    summaryVo.setLatestComment(comments.get(0).getFullMessage());
                }
            }
            summaryList.add(summaryVo);
        }

        return summaryList;
    }

    private WfProcessHistoryVo convertTaskToHistoryVo(HistoricTaskInstance task, Map<String, Comment> commentMap) {
        WfProcessHistoryVo vo = new WfProcessHistoryVo();
        vo.setActivityId(task.getTaskDefinitionKey());
        vo.setActivityName(task.getName());

        // 检查是否为合并的多实例任务
        if (task instanceof MergedHistoricTaskInstance) {
            MergedHistoricTaskInstance mergedTask = (MergedHistoricTaskInstance) task;
            convertMergedTaskToHistoryVo(vo, mergedTask, commentMap);
        } else if (task instanceof org.ahead4.workflow.domain.wrapper.RoundInfoTaskInstance) {
            // 带轮次信息的单实例任务
            org.ahead4.workflow.domain.wrapper.RoundInfoTaskInstance roundTask =
                (org.ahead4.workflow.domain.wrapper.RoundInfoTaskInstance) task;
            convertSingleTaskToHistoryVo(vo, roundTask.getDelegate(), commentMap);
            vo.setApprovalRound(roundTask.getApprovalRound());
            vo.setReapproval(roundTask.isReapproval());
        } else {
            // 普通单实例任务的处理逻辑
            convertSingleTaskToHistoryVo(vo, task, commentMap);
        }

        return vo;
    }

    /**
     * 转换单实例任务为历史记录VO
     *
     * @param vo 历史记录VO
     * @param task 单实例任务
     * @param commentMap 评论映射
     */
    private void convertSingleTaskToHistoryVo(WfProcessHistoryVo vo, HistoricTaskInstance task,
                                              Map<String, Comment> commentMap) {
        vo.setAssignee(task.getAssignee());
        if (StringUtils.isNotBlank(task.getAssignee())) {
            vo.setAssigneeName(adapter.getNickNameById(task.getAssignee()));
        }
        vo.setStartTime(task.getCreateTime());
        vo.setEndTime(task.getEndTime());

        Comment comment = commentMap.get(task.getId());
        if (Objects.nonNull(comment)) {
            WfCommentVo wfCommentVo = new WfCommentVo().setMessage(comment.getFullMessage())
                    .setType(comment.getType())
                    .setTime(comment.getTime());
            vo.setComment(wfCommentVo);
        } else {
            List<Comment> taskComments =
                    taskService.getTaskComments(task.getId(),
                     org.ahead4.workflow.enums.FlowComment.NORMAL.getType());
            if (CollUtil.isNotEmpty(taskComments)) {
                WfCommentVo wfCommentVo = new WfCommentVo().setMessage(taskComments.get(0).getFullMessage())
                        .setType(taskComments.get(0).getType()).setTime(taskComments.get(0).getTime());
                vo.setComment(wfCommentVo);
            }
        }
    }

    /**
     * 将合并的多实例任务转换为历史VO对象
     *
     * @param vo         目标VO对象
     * @param mergedTask 合并的任务实例
     * @param commentMap 评论映射
     */
    private void convertMergedTaskToHistoryVo(WfProcessHistoryVo vo, MergedHistoricTaskInstance mergedTask,
                                              Map<String, Comment> commentMap) {
        List<HistoricTaskInstance> allTasks = mergedTask.getAllTasks();

        // 设置多实例标识
        vo.setMultiInstance(true);
        vo.setMultiInstanceCount(allTasks.size());

        // 设置轮次信息
        vo.setApprovalRound(mergedTask.getApprovalRound());
        vo.setReapproval(mergedTask.isReapproval());

        // 收集所有处理人
        List<String> assignees = allTasks.stream()
                .map(HistoricTaskInstance::getAssignee)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        vo.setMultiInstanceAssignees(assignees);

        // 设置主要处理人（代表性任务的处理人）
        String mainAssignee = mergedTask.getRepresentative().getAssignee();
        vo.setAssignee(mainAssignee);
        if (StringUtils.isNotBlank(mainAssignee)) {
            vo.setAssigneeName(adapter.getNickNameById(mainAssignee));
        }

        // 计算时间范围
        Date earliestStart = allTasks.stream()
                .map(HistoricTaskInstance::getCreateTime)
                .filter(Objects::nonNull)
                .min(Date::compareTo)
                .orElse(null);

        Date latestEnd = allTasks.stream()
                .map(HistoricTaskInstance::getEndTime)
                .filter(Objects::nonNull)
                .max(Date::compareTo)
                .orElse(null);

        vo.setStartTime(earliestStart);
        vo.setEndTime(latestEnd);
        vo.setEarliestStartTime(earliestStart);
        vo.setLatestEndTime(latestEnd);

        // 收集所有评论
        List<WfCommentVo> allComments = allTasks.stream()
                .map(HistoricTaskInstance::getId)
                .map(commentMap::get)
                .filter(Objects::nonNull)
                .map(comment -> new WfCommentVo()
                        .setMessage(comment.getFullMessage())
                        .setType(comment.getType())
                        .setTime(comment.getTime()))
                .sorted(Comparator.comparing(WfCommentVo::getTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        vo.setMultiInstanceComments(allComments);

        // 设置主评论（最新的评论）
        if (!allComments.isEmpty()) {
            vo.setComment(allComments.get(0));
        }
    }

    @Override
    public List<WfDefinitionVo> selectStartProcessList(ProcessQuery processQuery) {
        // 流程定义列表数据查询
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery()
                .latestVersion()
                .active()
                .orderByProcessDefinitionKey()
                .asc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(processDefinitionQuery, processQuery);

        List<ProcessDefinition> definitionList = processDefinitionQuery.list();

        List<WfDefinitionVo> definitionVoList = new ArrayList<>();
        for (ProcessDefinition processDefinition : definitionList) {
            String deploymentId = processDefinition.getDeploymentId();
            Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
            WfDefinitionVo vo = getWfDefinitionVo(processDefinition, deployment);
            definitionVoList.add(vo);
        }
        return definitionVoList;
    }

    private static WfDefinitionVo getWfDefinitionVo(ProcessDefinition processDefinition, Deployment deployment) {
        WfDefinitionVo vo = new WfDefinitionVo();
        vo.setDefinitionId(processDefinition.getId());
        vo.setProcessKey(processDefinition.getKey());
        vo.setProcessName(processDefinition.getName());
        vo.setVersion(processDefinition.getVersion());
        vo.setDeploymentId(processDefinition.getDeploymentId());
        vo.setSuspended(processDefinition.isSuspended());
        // 流程定义时间
        vo.setCategory(deployment.getCategory());
        vo.setDeploymentTime(deployment.getDeploymentTime());
        return vo;
    }

    @Override
    public TableDataInfo<WfTaskVo> selectPageOwnProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfTaskVo> page = new Page<>();
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery()
            .startedBy(TaskUtils.getUserId())
            .orderByProcessInstanceStartTime()
            .desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(historicProcessInstanceQuery, processQuery);
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery
            .listPage(offset, pageQuery.getPageSize());
        page.setTotal(historicProcessInstanceQuery.count());
        List<WfTaskVo> taskVoList = new ArrayList<>();
        for (HistoricProcessInstance hisIns : historicProcessInstances) {
            WfTaskVo taskVo = new WfTaskVo();
            // 获取流程状态
            HistoricVariableInstance processStatusVariable = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(hisIns.getId())
                .variableName(ProcessConstants.PROCESS_STATUS_KEY)
                .singleResult();
            String processStatus = null;
            if (ObjectUtil.isNotNull(processStatusVariable)) {
                processStatus = Convert.toStr(processStatusVariable.getValue());
            }
            // 兼容旧流程
            if (processStatus == null) {
                processStatus = ObjectUtil.isNull(hisIns.getEndTime()) ? ProcessStatus.RUNNING.getStatus() : ProcessStatus.COMPLETED.getStatus();
            }
            taskVo.setProcessStatus(processStatus);
            taskVo.setCreateTime(hisIns.getStartTime());
            taskVo.setFinishTime(hisIns.getEndTime());
            taskVo.setProcInsId(hisIns.getId());

            // 计算耗时
            if (Objects.nonNull(hisIns.getEndTime())) {
                taskVo.setDuration(DateUtils.getDatePoor(hisIns.getEndTime(), hisIns.getStartTime()));
            } else {
                taskVo.setDuration(DateUtils.getDatePoor(DateUtils.getNowDate(), hisIns.getStartTime()));
            }
            // 流程部署实例信息
            Deployment deployment = repositoryService.createDeploymentQuery()
                .deploymentId(hisIns.getDeploymentId()).singleResult();
            taskVo.setDeployId(hisIns.getDeploymentId());
            taskVo.setProcDefId(hisIns.getProcessDefinitionId());
            taskVo.setProcDefName(hisIns.getProcessDefinitionName());
            taskVo.setProcDefVersion(hisIns.getProcessDefinitionVersion());
            taskVo.setCategory(deployment.getCategory());
            // 当前所处流程
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisIns.getId()).includeIdentityLinks().list();
            if (CollUtil.isNotEmpty(taskList)) {
                taskVo.setTaskName(taskList.stream().map(Task::getName).filter(StringUtils::isNotEmpty).collect(Collectors.joining(",")));
            }
            taskVoList.add(taskVo);
        }
        page.setRecords(taskVoList);
        return TableDataInfo.build(page);
    }

    @Override
    public List<WfTaskVo> selectOwnProcessList(ProcessQuery processQuery) {
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .startedBy(TaskUtils.getUserId())
                .orderByProcessInstanceStartTime()
                .desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(historicProcessInstanceQuery, processQuery);
        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery.list();
        List<WfTaskVo> taskVoList = new ArrayList<>();
        for (HistoricProcessInstance hisIns : historicProcessInstances) {
            WfTaskVo taskVo = new WfTaskVo();
            taskVo.setCreateTime(hisIns.getStartTime());
            taskVo.setFinishTime(hisIns.getEndTime());
            taskVo.setProcInsId(hisIns.getId());

            // 计算耗时
            if (Objects.nonNull(hisIns.getEndTime())) {
                taskVo.setDuration(DateUtils.getDatePoor(hisIns.getEndTime(), hisIns.getStartTime()));
            } else {
                taskVo.setDuration(DateUtils.getDatePoor(DateUtils.getNowDate(), hisIns.getStartTime()));
            }
            // 流程部署实例信息
            Deployment deployment = repositoryService.createDeploymentQuery()
                    .deploymentId(hisIns.getDeploymentId()).singleResult();
            taskVo.setDeployId(hisIns.getDeploymentId());
            taskVo.setProcDefId(hisIns.getProcessDefinitionId());
            taskVo.setProcDefName(hisIns.getProcessDefinitionName());
            taskVo.setProcDefVersion(hisIns.getProcessDefinitionVersion());
            taskVo.setCategory(deployment.getCategory());
            // 当前所处流程
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisIns.getId()).includeIdentityLinks().list();
            if (CollUtil.isNotEmpty(taskList)) {
                taskVo.setTaskName(taskList.stream().map(Task::getName).filter(StringUtils::isNotEmpty).collect(Collectors.joining(",")));
            }
            taskVoList.add(taskVo);
        }
        return taskVoList;
    }

    @Override
    public TableDataInfo<WfTaskVo> selectPageTodoProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfTaskVo> page = new Page<>();
        TaskQuery taskQuery = taskService.createTaskQuery()
            .active()
            .includeProcessVariables()
            .taskCandidateOrAssigned(TaskUtils.getUserId())
            .taskCandidateGroupIn(TaskUtils.getCandidateGroup())
            .orderByTaskCreateTime().desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskQuery, processQuery);
        page.setTotal(taskQuery.count());
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<Task> taskList = taskQuery.listPage(offset, pageQuery.getPageSize());
        List<WfTaskVo> flowList = new ArrayList<>();
        for (Task task : taskList) {
            WfTaskVo flowTask = new WfTaskVo();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .singleResult();
            String userId = historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            flowTask.setStartUserId(userId);
            flowTask.setStartUserName(nickName);
            flowTask.setAssigneeDeptCode((String) taskService.getVariable(task.getId(), "dept"));

            // 流程变量
            flowTask.setProcVars(task.getProcessVariables());
            Object variable = taskService.getVariable(task.getId(), "assignee");
            if (variable instanceof CCDto) {
                flowTask.setAssigneeInfo((CCDto) variable);
            }
            flowList.add(flowTask);
        }
        page.setRecords(flowList);
        return TableDataInfo.build(page);
    }

    @Override
    public List<WfTaskVo> selectTodoProcessList(ProcessQuery processQuery) {
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .taskCandidateOrAssigned(TaskUtils.getUserId())
                .taskCandidateGroupIn(TaskUtils.getCandidateGroup())
                .orderByTaskCreateTime().desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskQuery, processQuery);
        List<Task> taskList = taskQuery.list();
        List<WfTaskVo> taskVoList = new ArrayList<>();
        for (Task task : taskList) {
            WfTaskVo taskVo = new WfTaskVo();
            // 当前流程信息
            taskVo.setTaskId(task.getId());
            taskVo.setTaskDefKey(task.getTaskDefinitionKey());
            taskVo.setCreateTime(task.getCreateTime());
            taskVo.setProcDefId(task.getProcessDefinitionId());
            taskVo.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            taskVo.setDeployId(pd.getDeploymentId());
            taskVo.setProcDefName(pd.getName());
            taskVo.setProcDefVersion(pd.getVersion());
            taskVo.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            String userId =historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            taskVo.setStartUserId(userId);
            taskVo.setStartUserName(nickName);

            taskVoList.add(taskVo);
        }
        return taskVoList;
    }

    @Override
    public TableDataInfo<WfTaskVo> selectPageClaimProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfTaskVo> page = new Page<>();
        TaskQuery taskQuery = taskService.createTaskQuery()
            .active()
            .includeProcessVariables()
            .taskCandidateUser(TaskUtils.getUserId())
            .taskCandidateGroupIn(TaskUtils.getCandidateGroup())
            .orderByTaskCreateTime().desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskQuery, processQuery);
        page.setTotal(taskQuery.count());
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<Task> taskList = taskQuery.listPage(offset, pageQuery.getPageSize());
        List<WfTaskVo> flowList = new ArrayList<>();
        for (Task task : taskList) {
            WfTaskVo flowTask = new WfTaskVo();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .singleResult();
            String userId = historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            flowTask.setStartUserId(userId);
            flowTask.setStartUserName(nickName);

            flowList.add(flowTask);
        }
        page.setRecords(flowList);
        return TableDataInfo.build(page);
    }

    @Override
    public List<WfTaskVo> selectClaimProcessList(ProcessQuery processQuery) {
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .taskCandidateUser(TaskUtils.getUserId())
                .taskCandidateGroupIn(TaskUtils.getCandidateGroup())
                .orderByTaskCreateTime().desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskQuery, processQuery);
        List<Task> taskList = taskQuery.list();
        List<WfTaskVo> flowList = new ArrayList<>();
        for (Task task : taskList) {
            WfTaskVo flowTask = new WfTaskVo();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            String userId = historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            flowTask.setStartUserId(userId);
            flowTask.setStartUserName(nickName);

            flowList.add(flowTask);
        }
        return flowList;
    }

    @Override
    public TableDataInfo<WfTaskVo> selectPageFinishedProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        Page<WfTaskVo> page = new Page<>();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
            .includeProcessVariables()
            .finished()
            .taskAssignee(TaskUtils.getUserId())
            .orderByHistoricTaskInstanceEndTime()
            .desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskInstanceQuery, processQuery);
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(offset, pageQuery.getPageSize());
        List<WfTaskVo> hisTaskList = new ArrayList<>();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
            WfTaskVo flowTask = new WfTaskVo();
            // 当前流程信息
            flowTask.setTaskId(histTask.getId());
            // 审批人员信息
            flowTask.setCreateTime(histTask.getCreateTime());
            flowTask.setFinishTime(histTask.getEndTime());
            flowTask.setDuration(DateUtil.formatBetween(histTask.getDurationInMillis(), BetweenFormatter.Level.SECOND));
            flowTask.setProcDefId(histTask.getProcessDefinitionId());
            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
            flowTask.setTaskName(histTask.getName());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(histTask.getProcessDefinitionId())
                .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(histTask.getProcessInstanceId());
            flowTask.setHisProcInsId(histTask.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(histTask.getProcessInstanceId())
                .singleResult();
            String userId = historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            flowTask.setStartUserId(userId);
            flowTask.setStartUserName(nickName);

            // 流程变量
            flowTask.setProcVars(histTask.getProcessVariables());

            hisTaskList.add(flowTask);
        }
        page.setTotal(taskInstanceQuery.count());
        page.setRecords(hisTaskList);
//        Map<String, Object> result = new HashMap<>();
//        result.put("result",page);
//        result.put("finished",true);
        return TableDataInfo.build(page);
    }

    @Override
    public List<WfTaskVo> selectFinishedProcessList(ProcessQuery processQuery) {
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(TaskUtils.getUserId())
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskInstanceQuery, processQuery);
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.list();
        List<WfTaskVo> hisTaskList = new ArrayList<>();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
            WfTaskVo flowTask = new WfTaskVo();
            // 当前流程信息
            flowTask.setTaskId(histTask.getId());
            // 审批人员信息
            flowTask.setCreateTime(histTask.getCreateTime());
            flowTask.setFinishTime(histTask.getEndTime());
            flowTask.setDuration(DateUtil.formatBetween(histTask.getDurationInMillis(), BetweenFormatter.Level.SECOND));
            flowTask.setProcDefId(histTask.getProcessDefinitionId());
            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
            flowTask.setTaskName(histTask.getName());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(histTask.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(histTask.getProcessInstanceId());
            flowTask.setHisProcInsId(histTask.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId())
                    .singleResult();
            String userId = historicProcessInstance.getStartUserId();
            String nickName = adapter.getNickNameById(userId);
            flowTask.setStartUserId(userId);
            flowTask.setStartUserName(nickName);

            // 流程变量
            flowTask.setProcVars(histTask.getProcessVariables());

            hisTaskList.add(flowTask);
        }
        return hisTaskList;
    }

    @Override
    public FormConf selectFormContent(String definitionId, String deployId, String procInsId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(definitionId);
        if (ObjectUtil.isNull(bpmnModel)) {
            throw new RuntimeException("获取流程设计失败！");
        }
        StartEvent startEvent = ModelUtils.getStartEvent(bpmnModel);
        WfDeployForm deployForm = deployFormMapper.selectOne(new LambdaQueryWrapper<WfDeployForm>()
            .eq(WfDeployForm::getDeployId, deployId)
            .eq(WfDeployForm::getFormKey, startEvent.getFormKey())
            .eq(WfDeployForm::getNodeKey, startEvent.getId()));
        FormConf formConf = JsonUtils.parseObject(deployForm.getContent(), FormConf.class);
        if (ObjectUtil.isNull(formConf)) {
            throw new RuntimeException("获取流程表单失败！");
        }
        if (ObjectUtil.isNotEmpty(procInsId)) {
            // 获取流程实例
            HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInsId)
                .includeProcessVariables()
                .singleResult();
            // 填充表单信息
            ProcessFormUtils.fillFormData(formConf, historicProcIns.getProcessVariables());
        }
        return formConf;
    }

    /**
     * 根据流程定义ID启动流程实例
     *
     * @param procDefId 流程定义Id
     * @param variables 流程变量
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startProcessByDefId(String procDefId, String businessKey, Map<String, Object> variables) {
        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(procDefId).singleResult();
            return startProcess(processDefinition, businessKey, variables);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestException("流程启动错误");
        }
    }

    /**
     * 通过DefinitionKey启动流程
     * @param procDefKey 流程定义Key
     * @param variables 扩展参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startProcessByDefKey(String procDefKey, String businessKey, Map<String, Object> variables) {
        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(procDefKey).latestVersion().singleResult();
            return startProcess(processDefinition,businessKey, variables);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestException("流程启动错误：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessByIds(String[] instanceIds) {
        List<String> ids = Arrays.asList(instanceIds);
        // 校验流程是否结束
        long activeInsCount = runtimeService.createProcessInstanceQuery()
            .processInstanceIds(new HashSet<>(ids)).active().count();
        if (activeInsCount > 0) {
            throw new RestException("不允许删除进行中的流程实例");
        }
        // 删除历史流程实例
        historyService.bulkDeleteHistoricProcessInstances(ids);
    }

    /**
     * 读取xml文件
     * @param processDefId 流程定义ID
     */
    @Override
    public String queryBpmnXmlById(String processDefId) {
        InputStream inputStream = repositoryService.getProcessModel(processDefId);
        try {
            return IoUtil.readUtf8(inputStream);
        } catch (IORuntimeException exception) {
            throw new RuntimeException("加载xml文件异常");
        }
    }

    /**
     * 流程详情信息
     *
     * @param procInsId 流程实例ID
     * @param taskId 任务ID
     * @return
     */
    @Override
    public WfDetailVo queryProcessDetail(String procInsId, String taskId) {
        WfDetailVo detailVo = new WfDetailVo();
        // 获取流程实例
        HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(procInsId)
            .includeProcessVariables()
            .singleResult();
        if (StringUtils.isNotBlank(taskId)) {
            HistoricTaskInstance taskIns = historyService.createHistoricTaskInstanceQuery()
                .taskId(taskId)
                .includeIdentityLinks()
                .includeProcessVariables()
                .includeTaskLocalVariables()
                .singleResult();
            if (taskIns == null) {
                throw new RestException("没有可办理的任务！");
            }
            detailVo.setTaskFormData(currTaskFormData(historicProcIns.getDeploymentId(), taskIns));
        }
        // 获取Bpmn模型信息
        InputStream inputStream = repositoryService.getProcessModel(historicProcIns.getProcessDefinitionId());
        String bpmnXmlStr = StrUtil.utf8Str(IoUtil.readBytes(inputStream, false));
        BpmnModel bpmnModel = ModelUtils.getBpmnModel(bpmnXmlStr);
        detailVo.setBpmnXml(bpmnXmlStr);
        detailVo.setHistoryProcNodeList(historyProcNodeList(historicProcIns));
        detailVo.setProcessFormList(processFormList(bpmnModel, historicProcIns));
        detailVo.setFlowViewer(getFlowViewer(bpmnModel, procInsId));
        return detailVo;
    }

    /**
     * 按 proc inst ID 查询进程详细信息
     *
     * @param procInstId proc inst id
     * @return {@link List }<{@link WfProcNodeVo }>
     */
    @Override
    public List<WfProcNodeVo> queryProcessDetailByProcInstId(String procInstId) {
        // 获取流程实例
        HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(procInstId)
            .includeProcessVariables()
            .singleResult();

        if (historicProcIns == null) {
            throw new RestException("流程实例不存在");
        }

        // 获取Bpmn模型信息
        InputStream inputStream = repositoryService.getProcessModel(historicProcIns.getProcessDefinitionId());
        String bpmnXmlStr = StrUtil.utf8Str(IoUtil.readBytes(inputStream, false));
        BpmnModel bpmnModel = ModelUtils.getBpmnModel(bpmnXmlStr);

        // 获取流程定义中的所有用户任务
        Collection<UserTask> allUserTasks = ModelUtils.getAllUserTaskEvent(bpmnModel);

        // 查询已完成和进行中的用户任务
        List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
            .processInstanceId(procInstId)
            .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
            .orderByHistoricActivityInstanceStartTime().asc()
            .list();

        // 查询当前活动的任务
        List<Task> activeTasks = taskService.createTaskQuery()
            .processInstanceId(procInstId)
            .active()
            .list();

        // 收集当前活动任务的ID，用于标识当前处理的任务
        Set<String> activeTaskDefinitionKeys = activeTasks.stream()
            .map(Task::getTaskDefinitionKey)
            .collect(Collectors.toSet());

        // 收集已完成和进行中的用户任务的定义键
        Set<String> processedTaskKeys = historicActivityInstanceList.stream()
            .map(HistoricActivityInstance::getActivityId)
            .collect(Collectors.toSet());

        // 获取流程评论
        List<Comment> commentList = taskService.getProcessInstanceComments(procInstId);

        // 构建结果列表
        List<WfProcNodeVo> procNodeVoList = new ArrayList<>();

        // 添加开始事件
        // HistoricActivityInstance startEvent = historyService.createHistoricActivityInstanceQuery()
        //     .processInstanceId(procInstId)
        //     .activityType(BpmnXMLConstants.ELEMENT_EVENT_START)
        //     .singleResult();

        // if (startEvent != null) {
        //     WfProcNodeVo startNodeVo = new WfProcNodeVo();
        //     startNodeVo.setProcDefId(startEvent.getProcessDefinitionId());
        //     startNodeVo.setActivityId(startEvent.getActivityId());
        //     startNodeVo.setActivityName(startEvent.getActivityName());
        //     startNodeVo.setActivityType(startEvent.getActivityType());
        //     startNodeVo.setCreateTime(startEvent.getStartTime());
        //     startNodeVo.setEndTime(startEvent.getEndTime());
        //
        //     if (ObjectUtil.isNotNull(startEvent.getDurationInMillis())) {
        //         startNodeVo.setDuration(DateUtil.formatBetween(startEvent.getDurationInMillis(), BetweenFormatter.Level.SECOND));
        //     }
        //
        //     // 设置发起人信息
        //     if (ObjectUtil.isNotNull(historicProcIns)) {
        //         String userId = historicProcIns.getStartUserId();
        //         String nickName = adapter.getNickNameById(userId);
        //         if (nickName != null) {
        //             startNodeVo.setAssigneeId(userId);
        //             startNodeVo.setAssigneeName(nickName);
        //         }
        //     }
        //
        //     // 设置为非活动状态
        //     startNodeVo.setIsActive(false);
        //
        //     procNodeVoList.add(startNodeVo);
        // }

        // 按任务定义键分组处理已执行的用户任务，处理多实例任务
        Map<String, List<HistoricActivityInstance>> taskDefinitionMap = historicActivityInstanceList.stream()
            .collect(Collectors.groupingBy(HistoricActivityInstance::getActivityId));

        // 尝试从流程变量中获取CCDto对象
        Map<String, Object> processVariables = null;
        try {
            // 获取流程实例的变量
            processVariables = runtimeService.getVariables(procInstId);
        } catch (Exception e) {
            // 流程可能已经完成，尝试从历史流程实例中获取变量
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(procInstId)
                    .includeProcessVariables()
                    .singleResult();
            if (historicProcessInstance != null) {
                processVariables = historicProcessInstance.getProcessVariables();
            }
        }

        // 先处理流程定义中的所有用户任务
        for (UserTask userTask : allUserTasks) {
            String taskDefinitionKey = userTask.getId();

            // 创建任务节点
            WfProcNodeVo nodeVo = new WfProcNodeVo();
            nodeVo.setProcDefId(historicProcIns.getProcessDefinitionId());
            nodeVo.setActivityId(taskDefinitionKey);
            nodeVo.setActivityName(userTask.getName());
            nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
            nodeVo.setDocumentation(userTask.getDocumentation());

            // 检查是否为多实例任务
            boolean isMultiInstance = userTask.hasMultiInstanceLoopCharacteristics();

            // 如果这个任务已经执行过，则使用历史数据
            if (processedTaskKeys.contains(taskDefinitionKey)) {
                List<HistoricActivityInstance> instances = taskDefinitionMap.get(taskDefinitionKey);

                // 使用第一个实例的基本信息
                HistoricActivityInstance firstInstance = instances.get(0);
                nodeVo.setCreateTime(firstInstance.getStartTime());

                // 对于多实例任务，只有所有实例都完成时，才设置结束时间
                if (isMultiInstance) {
                    // 检查是否所有实例都已完成
                    boolean allCompleted = instances.stream().allMatch(instance -> instance.getEndTime() != null);
                    if (allCompleted) {
                        // 使用最后一个完成的实例的结束时间
                        HistoricActivityInstance lastInstance = instances.stream()
                            .max(Comparator.comparing(HistoricActivityInstance::getEndTime))
                            .orElse(firstInstance);
                        nodeVo.setEndTime(lastInstance.getEndTime());

                        // 计算总耗时
                        if (nodeVo.getCreateTime() != null && nodeVo.getEndTime() != null) {
                            long durationInMillis = nodeVo.getEndTime().getTime() - nodeVo.getCreateTime().getTime();
                            nodeVo.setDuration(DateUtil.formatBetween(durationInMillis, BetweenFormatter.Level.SECOND));
                        }
                    }
                } else {
                    // 非多实例任务直接使用第一个实例的结束时间
                    nodeVo.setEndTime(firstInstance.getEndTime());

                    if (ObjectUtil.isNotNull(firstInstance.getDurationInMillis())) {
                        nodeVo.setDuration(DateUtil.formatBetween(firstInstance.getDurationInMillis(), BetweenFormatter.Level.SECOND));
                    }
                }

                // 收集所有实例的处理人信息
                Set<String> assigneeIds = new LinkedHashSet<>();
                Set<String> assigneeNames = new LinkedHashSet<>();

                for (HistoricActivityInstance instance : instances) {
                    if (StringUtils.isNotBlank(instance.getAssignee())) {
                        String userId = instance.getAssignee();
                        String nickName = adapter.getNickNameById(userId);
                        assigneeIds.add(userId);
                        if (nickName != null) {
                            assigneeNames.add(nickName);
                        }
                    }
                }

                // 将所有处理人信息合并
                if (!assigneeIds.isEmpty()) {
                    nodeVo.setAssigneeId(String.join(",", assigneeIds));
                    nodeVo.setAssigneeName(String.join(",", assigneeNames));
                }

                // 收集所有实例的候选人信息
//                Set<String> candidates = new LinkedHashSet<>();

//                for (HistoricActivityInstance instance : instances) {
//                    List<HistoricIdentityLink> linksForTask = historyService.getHistoricIdentityLinksForTask(instance.getTaskId());
//                    for (HistoricIdentityLink identityLink : linksForTask) {
//                        if ("candidate".equals(identityLink.getType())) {
//                            if (StringUtils.isNotBlank(identityLink.getUserId())) {
//                                String userId = identityLink.getUserId();
//                                String nickName = adapter.getNickNameById(userId);
//                                if (nickName != null) {
//                                    candidates.add(nickName);
//                                }
//                            }
//                            if (StringUtils.isNotBlank(identityLink.getGroupId())) {
//                                if (identityLink.getGroupId().startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
//                                    String roleId = StringUtils.stripStart(identityLink.getGroupId(),
//                                            TaskConstants.ROLE_GROUP_PREFIX);
//                                    final String roleName = adapter.getRoleNameById(roleId);
//                                    if (roleName != null) {
//                                        candidates.add(roleName);
//                                    }
//                                } else if (identityLink.getGroupId().startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
//                                    String deptId = StringUtils.stripStart(identityLink.getGroupId(), TaskConstants.DEPT_GROUP_PREFIX);
//                                    final String deptName = adapter.getDeptNameById(deptId);
//                                    if (deptName != null) {
//                                        candidates.add(deptName);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }

//                if (!candidates.isEmpty()) {
//                    nodeVo.setCandidate(String.join(",", candidates));
//                }


                // 收集所有实例的评论
                if (CollUtil.isNotEmpty(commentList)) {
                    List<FlowComment> taskComments = new ArrayList<>();
                    List<CCDto> users = new ArrayList<>();
                    for (HistoricActivityInstance instance : instances) {
                        Map<String, Object> finalProcessVariables = processVariables;
                        if (finalProcessVariables != null && finalProcessVariables.containsKey(instance.getActivityId())) {
                            Object ccDtoObj = finalProcessVariables.get(instance.getActivityId());
                            if (ccDtoObj instanceof JSONArray) {
                                JSONArray array = (JSONArray) ccDtoObj;
                                users.addAll(array.toJavaList(CCDto.class));
                            } else if ( ccDtoObj instanceof JSONObject) {
                                JSONObject jsonObject = (JSONObject) ccDtoObj;
                                users.add(jsonObject.toJavaObject(CCDto.class));
                            }
                        }
                        List<FlowComment> flowComments = commentList.stream()
                                .filter(comment -> comment.getTaskId().equals(instance.getTaskId()))
                                .map(comment -> {
                                    FlowComment flowComment = new FlowComment();
                                    BeanUtil.copyProperties(comment, flowComment);
                                    // 获取用户ID
                                    String userId = comment.getUserId();
                                    // 从流程变量中获取以taskId为key的CCDto对象
                                    if (CollectionUtils.isNotEmpty(users)) {
                                        users.forEach(u -> {
                                            // 如果用户ID匹配，则使用CCDto中的信息
                                            if (u.getUsername().equals(userId)) {
                                                flowComment.setDisplayname(u.getDisplayname());
                                                flowComment.setDeptCode(u.getDeptCode());
                                                flowComment.setDeptName(u.getDeptName());
                                            }
                                        });
                                    }

                                    // 如果没有从任务变量中获取到用户信息，则使用adapter获取
                                    if (StringUtils.isBlank(flowComment.getDisplayname())) {
                                        String nickName = adapter.getNickNameById(userId);
                                        flowComment.setDisplayname(nickName);
                                    }
                                    return flowComment;
                                })
                                .collect(Collectors.toList());
                        taskComments.addAll(flowComments);
                    }

                    // 按时间排序评论
                    taskComments.sort(Comparator.comparing(FlowComment::getTime));
                    nodeVo.setCommentList(taskComments);
                }
            } else {
                // 如果任务尚未执行，则使用模型中的候选人信息
                // 获取候选人信息
                List<String> candidateUsers = userTask.getCandidateUsers();
                List<String> candidateGroups = userTask.getCandidateGroups();

                if (CollUtil.isNotEmpty(candidateUsers) || CollUtil.isNotEmpty(candidateGroups)) {
                    Set<String> candidates = new LinkedHashSet<>();

                    // 处理候选用户
                    if (CollUtil.isNotEmpty(candidateUsers)) {
                        for (String userId : candidateUsers) {
                            String nickName = adapter.getNickNameById(userId);
                            if (nickName != null) {
                                candidates.add(nickName);
                            }
                        }
                    }

                    // 处理候选组
                    if (CollUtil.isNotEmpty(candidateGroups)) {
                        for (String groupId : candidateGroups) {
                            if (groupId.startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
                                String roleId = StringUtils.stripStart(groupId, TaskConstants.ROLE_GROUP_PREFIX);
                                final String roleName = adapter.getRoleNameById(roleId);
                                if (roleName != null) {
                                    candidates.add(roleName);
                                }
                            } else if (groupId.startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
                                String deptId = StringUtils.stripStart(groupId, TaskConstants.DEPT_GROUP_PREFIX);
                                final String deptName = adapter.getDeptNameById(deptId);
                                if (deptName != null) {
                                    candidates.add(deptName);
                                }
                            }
                        }
                    }

                    if (!candidates.isEmpty()) {
                        nodeVo.setCandidate(String.join(",", candidates));
                    }
                }

                // 如果有指定的处理人
                String assignee = userTask.getAssignee();
                if (StringUtils.isNotBlank(assignee)) {
                    String nickName = adapter.getNickNameById(assignee);
                    nodeVo.setAssigneeId(assignee);
                    if (nickName != null) {
                        nodeVo.setAssigneeName(nickName);
                    }
                }
            }

            // 标识当前正在处理的任务
            nodeVo.setIsActive(activeTaskDefinitionKeys.contains(taskDefinitionKey));

            procNodeVoList.add(nodeVo);
        }

        // 我们不需要额外排序，因为我们按照流程定义中的顺序处理任务

        // 添加结束事件（如果流程已结束）
        // if (historicProcIns.getEndTime() != null) {
        //     HistoricActivityInstance endEvent = historyService.createHistoricActivityInstanceQuery()
        //         .processInstanceId(procInstId)
        //         .activityType(BpmnXMLConstants.ELEMENT_EVENT_END)
        //         .singleResult();
        //
        //     if (endEvent != null) {
        //         WfProcNodeVo endNodeVo = new WfProcNodeVo();
        //         endNodeVo.setProcDefId(endEvent.getProcessDefinitionId());
        //         endNodeVo.setActivityId(endEvent.getActivityId());
        //         endNodeVo.setActivityName(endEvent.getActivityName());
        //         endNodeVo.setActivityType(endEvent.getActivityType());
        //         endNodeVo.setCreateTime(endEvent.getStartTime());
        //         endNodeVo.setEndTime(endEvent.getEndTime());
        //
        //         if (ObjectUtil.isNotNull(endEvent.getDurationInMillis())) {
        //             endNodeVo.setDuration(DateUtil.formatBetween(endEvent.getDurationInMillis(), BetweenFormatter.Level.SECOND));
        //         }
        //
        //         // 设置为非活动状态
        //         endNodeVo.setIsActive(false);
        //
        //         procNodeVoList.add(endNodeVo);
        //     }
        // }

        return procNodeVoList;
    }

    /**
     * 启动流程实例
     */
    private String startProcess(ProcessDefinition procDef, String businessKey, Map<String, Object> variables) {
        if (ObjectUtil.isNull(procDef)) {
            throw new RestException("流程定义不存在");
        }
        if (ObjectUtil.isNotNull(procDef) && procDef.isSuspended()) {
            throw new RestException("流程已被挂起，请先激活流程");
        }
        // 设置流程发起人Id到流程中
        String userIdStr = TaskUtils.getUserId();
        identityService.setAuthenticatedUserId(userIdStr);
        variables.put(BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR, userIdStr);
        // 设置流程状态为进行中
        variables.put(ProcessConstants.PROCESS_STATUS_KEY, ProcessStatus.RUNNING.getStatus());
        // 发起流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDef.getId(),
                businessKey, variables);
        // 第一个用户任务为发起人，则自动完成任务
        // 获取 抄送信息，默认为空列表
        List<CCDto> makeCopyList;
        Object makeCopyObj = variables.get("makeCopy");
        if (makeCopyObj instanceof JSONArray) {
            makeCopyList = ((JSONArray) makeCopyObj).toJavaList(CCDto.class);
        } else if (makeCopyObj instanceof List) {
            makeCopyList = JSONArray.parseArray(JSON.toJSONString(makeCopyObj), CCDto.class);
        } else {
            makeCopyList = Collections.emptyList();
        }
        wfTaskService.startFirstTask(processInstance, variables, makeCopyList);
        return processInstance.getId();
    }


    /**
     * 获取流程变量
     *
     * @param taskId 任务ID
     * @return 流程变量
     */
    private Map<String, Object> getProcessVariables(String taskId) {
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
            .includeProcessVariables()
            .finished()
            .taskId(taskId)
            .singleResult();
        if (Objects.nonNull(historicTaskInstance)) {
            return historicTaskInstance.getProcessVariables();
        }
        return taskService.getVariables(taskId);
    }

    /**
     * 获取当前任务流程表单信息
     */
    private FormConf currTaskFormData(String deployId, HistoricTaskInstance taskIns) {
        WfDeployForm deployForm = deployFormMapper.selectOne(new LambdaQueryWrapper<WfDeployForm>()
            .eq(WfDeployForm::getDeployId, deployId)
            .eq(WfDeployForm::getFormKey, taskIns.getFormKey())
            .eq(WfDeployForm::getNodeKey, taskIns.getTaskDefinitionKey()));
        if (ObjectUtil.isNotEmpty(deployForm)) {
            FormConf currTaskFormData = JsonUtils.parseObject(deployForm.getContent(), FormConf.class);
            if (null != currTaskFormData) {
                currTaskFormData.setFormBtns(false);
                ProcessFormUtils.fillFormData(currTaskFormData, taskIns.getTaskLocalVariables());
                return currTaskFormData;
            }
        }
        return null;
    }

    /**
     * 获取历史流程表单信息
     */
    private List<FormConf> processFormList(BpmnModel bpmnModel, HistoricProcessInstance historicProcIns) {
        List<FormConf> procFormList = new ArrayList<>();

        List<HistoricActivityInstance> activityInstanceList = historyService.createHistoricActivityInstanceQuery()
            .processInstanceId(historicProcIns.getId()).finished()
            .activityTypes(CollUtil.newHashSet(BpmnXMLConstants.ELEMENT_EVENT_START, BpmnXMLConstants.ELEMENT_TASK_USER))
            .orderByHistoricActivityInstanceStartTime().asc()
            .list();
        List<String> processFormKeys = new ArrayList<>();
        for (HistoricActivityInstance activityInstance : activityInstanceList) {
            // 获取当前节点流程元素信息
            FlowElement flowElement = ModelUtils.getFlowElementById(bpmnModel, activityInstance.getActivityId());
            // 获取当前节点表单Key
            String formKey = ModelUtils.getFormKey(flowElement);
            if (formKey == null) {
                continue;
            }
            boolean localScope = Convert.toBool(ModelUtils.getElementAttributeValue(flowElement, ProcessConstants.PROCESS_FORM_LOCAL_SCOPE), false);
            Map<String, Object> variables;
            if (localScope) {
                // 查询任务节点参数，并转换成Map
                variables = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(historicProcIns.getId())
                    .taskId(activityInstance.getTaskId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(HistoricVariableInstance::getVariableName, HistoricVariableInstance::getValue));
            } else {
                if (processFormKeys.contains(formKey)) {
                    continue;
                }
                variables = historicProcIns.getProcessVariables();
                processFormKeys.add(formKey);
            }
            // 非节点表单此处查询结果可能有多条，只获取第一条信息
            List<WfDeployForm> formInfoList = deployFormMapper.selectList(new LambdaQueryWrapper<WfDeployForm>()
                .eq(WfDeployForm::getDeployId, historicProcIns.getDeploymentId())
                .eq(WfDeployForm::getFormKey, formKey)
                .eq(localScope, WfDeployForm::getNodeKey, flowElement.getId()));

            //@update by Brath：避免空集合导致的NULL空指针
            WfDeployForm formInfo = formInfoList.stream().findFirst().orElse(null);

            if (ObjectUtil.isNotNull(formInfo)) {
                // 旧数据 formInfo.getFormName() 为 null
                String formName = Optional.ofNullable(formInfo.getFormName()).orElse(StringUtils.EMPTY);
                String title = localScope ? formName.concat("(" + flowElement.getName() + ")") : formName;
                FormConf formConf = JsonUtils.parseObject(formInfo.getContent(), FormConf.class);
                if (null != formConf) {
                    formConf.setTitle(title);
                    formConf.setDisabled(true);
                    formConf.setFormBtns(false);
                    ProcessFormUtils.fillFormData(formConf, variables);
                    procFormList.add(formConf);
                }
            }
        }
        return procFormList;
    }

    /**
     * 获取历史任务信息列表
     */
    private List<WfProcNodeVo> historyProcNodeList(HistoricProcessInstance historicProcIns) {
        String procInsId = historicProcIns.getId();
        List<HistoricActivityInstance> historicActivityInstanceList =  historyService.createHistoricActivityInstanceQuery()
            .processInstanceId(procInsId)
            .activityTypes(CollUtil.newHashSet(BpmnXMLConstants.ELEMENT_EVENT_START, BpmnXMLConstants.ELEMENT_EVENT_END, BpmnXMLConstants.ELEMENT_TASK_USER))
            .orderByHistoricActivityInstanceStartTime().desc()
            .orderByHistoricActivityInstanceEndTime().desc()
            .list();

        List<Comment> commentList = taskService.getProcessInstanceComments(procInsId);

        List<WfProcNodeVo> elementVoList = new ArrayList<>();
        for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
            WfProcNodeVo elementVo = new WfProcNodeVo();
            elementVo.setProcDefId(activityInstance.getProcessDefinitionId());
            elementVo.setActivityId(activityInstance.getActivityId());
            elementVo.setActivityName(activityInstance.getActivityName());
            elementVo.setActivityType(activityInstance.getActivityType());
            elementVo.setCreateTime(activityInstance.getStartTime());
            elementVo.setEndTime(activityInstance.getEndTime());
            if (ObjectUtil.isNotNull(activityInstance.getDurationInMillis())) {
                elementVo.setDuration(DateUtil.formatBetween(activityInstance.getDurationInMillis(), BetweenFormatter.Level.SECOND));
            }

            if (BpmnXMLConstants.ELEMENT_EVENT_START.equals(activityInstance.getActivityType())) {
                if (ObjectUtil.isNotNull(historicProcIns)) {
                    String userId = historicProcIns.getStartUserId();
                    String nickName = adapter.getNickNameById(userId);
                    if (nickName != null) {
                        elementVo.setAssigneeId(userId);
                        elementVo.setAssigneeName(nickName);
                    }
                }
            } else if (BpmnXMLConstants.ELEMENT_TASK_USER.equals(activityInstance.getActivityType())) {
                if (StringUtils.isNotBlank(activityInstance.getAssignee())) {
                    String userId = activityInstance.getAssignee();
                    String nickName = adapter.getNickNameById(userId);
                    elementVo.setAssigneeId(userId);
                    elementVo.setAssigneeName(nickName);
                }
                // 展示审批人员
                List<HistoricIdentityLink> linksForTask = historyService.getHistoricIdentityLinksForTask(activityInstance.getTaskId());
                StringBuilder stringBuilder = new StringBuilder();
                for (HistoricIdentityLink identityLink : linksForTask) {
                    if ("candidate".equals(identityLink.getType())) {
                        if (StringUtils.isNotBlank(identityLink.getUserId())) {
                            String userId = identityLink.getUserId();
                            String nickName = adapter.getNickNameById(userId);
                            stringBuilder.append(nickName).append(",");
                        }
                        if (StringUtils.isNotBlank(identityLink.getGroupId())) {
                            if (identityLink.getGroupId().startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
                                String roleId = StringUtils.stripStart(identityLink.getGroupId(),
                                        TaskConstants.ROLE_GROUP_PREFIX);
                                final String roleName = adapter.getRoleNameById(roleId);
                                // SysRole role = roleService.selectRoleById(roleId);
                                stringBuilder.append(roleName).append(",");
                            } else if (identityLink.getGroupId().startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
                                String deptId = StringUtils.stripStart(identityLink.getGroupId(), TaskConstants.DEPT_GROUP_PREFIX);
                                // SysDept dept = deptService.selectDeptById(deptId);
                                final String deptName = adapter.getDeptNameById(deptId);
                                stringBuilder.append(deptName).append(",");
                            }
                        }
                    }
                }
                if (StringUtils.isNotBlank(stringBuilder)) {
                    elementVo.setCandidate(stringBuilder.substring(0, stringBuilder.length() - 1));
                }
                // 获取意见评论内容
                if (CollUtil.isNotEmpty(commentList)) {
                    List<Comment> comments = new ArrayList<>();
                    for (Comment comment : commentList) {

                        if (comment.getTaskId().equals(activityInstance.getTaskId())) {
                            comments.add(comment);
                        }
                    }
//                    elementVo.setCommentList(comments);
                }
            }
            elementVoList.add(elementVo);
        }
        return elementVoList;
    }

    /**
     * 获取流程执行过程
     *
     * @param procInsId
     * @return
     */
    private WfViewerVo getFlowViewer(BpmnModel bpmnModel, String procInsId) {
        // 构建查询条件
        HistoricActivityInstanceQuery query = historyService.createHistoricActivityInstanceQuery()
            .processInstanceId(procInsId);
        List<HistoricActivityInstance> allActivityInstanceList = query.list();
        if (CollUtil.isEmpty(allActivityInstanceList)) {
            return new WfViewerVo();
        }
        // 查询所有已完成的元素
        List<HistoricActivityInstance> finishedElementList = allActivityInstanceList.stream()
            .filter(item -> ObjectUtil.isNotNull(item.getEndTime())).collect(Collectors.toList());
        // 所有已完成的连线
        Set<String> finishedSequenceFlowSet = new HashSet<>();
        // 所有已完成的任务节点
        Set<String> finishedTaskSet = new HashSet<>();
        finishedElementList.forEach(item -> {
            if (BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW.equals(item.getActivityType())) {
                finishedSequenceFlowSet.add(item.getActivityId());
            } else {
                finishedTaskSet.add(item.getActivityId());
            }
        });
        // 查询所有未结束的节点
        Set<String> unfinishedTaskSet = allActivityInstanceList.stream()
            .filter(item -> ObjectUtil.isNull(item.getEndTime()))
            .map(HistoricActivityInstance::getActivityId)
            .collect(Collectors.toSet());
        // DFS 查询未通过的元素集合
        Set<String> rejectedSet = FlowableUtils.dfsFindRejects(bpmnModel, unfinishedTaskSet, finishedSequenceFlowSet, finishedTaskSet);
        return new WfViewerVo(finishedTaskSet, finishedSequenceFlowSet, unfinishedTaskSet, rejectedSet);
    }

    /**
     * 获取主流程中的子流程节点
     *
     * @param bpmnModel BPMN模型
     * @return 子流程节点集合
     */
    private Collection<SubProcess> getMainProcessSubProcesses(BpmnModel bpmnModel) {
        Process mainProcess = bpmnModel.getMainProcess();
        Collection<FlowElement> flowElements = mainProcess.getFlowElements();
        Collection<SubProcess> subProcesses = new ArrayList<>();

        for (FlowElement flowElement : flowElements) {
            if (flowElement instanceof SubProcess) {
                subProcesses.add((SubProcess) flowElement);
            }
        }
        return subProcesses;
    }

    /**
     * 获取流程详情的主入口方法（保持向后兼容）。
     */
    @Override
    public List<WfProcNodeVo> queryProcessDetailsWithOptimizedSubProcessHandling(String procInstId) {
        return queryProcessDetails(procInstId, null);
    }

    /**
     * 通用的流程详情查询方法
     * 支持查询主流程实例和子流程实例的详细信息
     *
     * @param procInstId           主流程实例ID
     * @param subProcessInstanceId 子流程实例ID，为空时查询主流程详情
     * @return 流程详情节点列表
     */
    public List<WfProcNodeVo> queryProcessDetails(String procInstId, String subProcessInstanceId) {
        if (StringUtils.isNotBlank(subProcessInstanceId)) {
            // 查询调用子流程实例详情
            return querySubProcessTaskDetails(procInstId, subProcessInstanceId);
        } else {
            // 查询主流程详情
            return queryMainProcessTaskDetails(procInstId);
        }
    }

    /**
     * 查询调用子流程实例的任务详情
     *
     * @param procInstId           主流程实例ID
     * @param subProcessInstanceId 调用子流程的历史活动实例ID（从selectPageSubProcessInstances返回的instanceId）
     * @return 调用子流程的任务节点列表
     */
    private List<WfProcNodeVo> querySubProcessTaskDetails(String procInstId, String subProcessInstanceId) {
        // 1. 根据历史活动实例ID获取调用活动的信息
        HistoricActivityInstance callActivityInstance = historyService.createHistoricActivityInstanceQuery()
                .activityInstanceId(subProcessInstanceId)
                .activityType(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY) // 确保是调用活动
                .singleResult();

        if (callActivityInstance == null) {
            throw new RestException("未找到指定的调用子流程实例，Activity Instance ID: " + subProcessInstanceId);
        }

        // 2. 获取被调用的子流程实例ID
        String calledProcessInstanceId = callActivityInstance.getCalledProcessInstanceId();
        if (StringUtils.isBlank(calledProcessInstanceId)) {
            throw new RestException("调用子流程实例未找到被调用的流程实例ID");
        }

        // 3. 获取被调用子流程的BPMN模型
        HistoricProcessInstance calledProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(calledProcessInstanceId).singleResult();
        if (calledProcessInstance == null) {
            throw new RestException("未找到被调用的子流程实例: " + calledProcessInstanceId);
        }
        
        BpmnModel subProcessBpmnModel = repositoryService.getBpmnModel(calledProcessInstance.getProcessDefinitionId());
        Process subProcess = subProcessBpmnModel.getMainProcess();

        // 4. 判断子流程实例是否已结束
        boolean isSubProcessCompleted = calledProcessInstance.getEndTime() != null;

        // 5. 查询被调用子流程实例的活动任务
        List<Task> activeTasks = taskService.createTaskQuery()
                .processInstanceId(calledProcessInstanceId)
                .active()
                .list();

        // 6. 查询被调用子流程实例的历史活动
        List<HistoricActivityInstance> historicActivities = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(calledProcessInstanceId)
                .list();

        // 7. 根据子流程实例状态决定要显示的用户任务定义
        Collection<UserTask> taskDefs;
        if (isSubProcessCompleted) {
            // 子流程已结束：只显示实际执行过的用户任务节点
            Set<String> executedTaskIds = historicActivities.stream()
                    .filter(h -> "userTask".equals(h.getActivityType()))
                    .map(HistoricActivityInstance::getActivityId)
                    .collect(Collectors.toSet());
            
            Collection<UserTask> allTaskDefs = ModelUtils.getAllUserTaskEvent(subProcess.getFlowElements(), new ArrayList<>());
            taskDefs = allTaskDefs.stream()
                    .filter(task -> executedTaskIds.contains(task.getId()))
                    .collect(Collectors.toList());
        } else {
            // 子流程未结束：显示所有定义的用户任务节点
            taskDefs = ModelUtils.getAllUserTaskEvent(subProcess.getFlowElements(), new ArrayList<>());
        }

        // 8. 过滤历史活动，只保留用户任务相关的
        List<HistoricActivityInstance> filteredHistoricActivities = historicActivities.stream()
                .filter(h -> taskDefs.stream().anyMatch(def -> def.getId().equals(h.getActivityId())))
                .collect(Collectors.toList());

        // 9. 查询被调用子流程的流程评论
        List<Comment> allComments = taskService.getProcessInstanceComments(calledProcessInstanceId);

        // 10. 使用增强版构建器构建任务节点列表
        return buildTaskNodeListEnhanced(taskDefs, filteredHistoricActivities, activeTasks, allComments);
    }

    /**
     * 查询主流程的用户任务详情（优化版本）
     * 包括已完成、进行中、未开始的用户任务，对于子流程只展示简单的流程及实例信息
     * 按照流程定义的顺序组装返回值
     */
    @Override
    public List<WfProcNodeVo> queryMainProcessTaskDetails(String procInstId) {
        HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstId)
                .includeProcessVariables()
                .singleResult();
        if (historicProcIns == null) {
            throw new RestException("流程实例不存在");
        }
        List<WfProcNodeVo> procNodeVoList = new ArrayList<>();

        // 一次性获取所有数据，提高性能
        List<HistoricActivityInstance> allHistoricActivities = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId).list();
        List<Task> allActiveTasks = taskService.createTaskQuery().processInstanceId(procInstId).active().list();
        List<Comment> allComments = taskService.getProcessInstanceComments(procInstId);

        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcIns.getProcessDefinitionId());
        Process mainProcess = bpmnModel.getMainProcess();
        Collection<FlowElement> flowElements = mainProcess.getFlowElements();


        // 判断流程是否已完成
        boolean isProcessCompleted = historicProcIns.getEndTime() != null;

        // 获取主流程的用户任务定义
        Collection<UserTask> mainProcessTaskDefs = ModelUtils.getMainProcessUserTaskEvent(bpmnModel);
        List<HistoricActivityInstance> mainHistoricActivities = allHistoricActivities.stream()
                .filter(h -> mainProcessTaskDefs.stream().anyMatch(def -> def.getId().equals(h.getActivityId())))
                .collect(Collectors.toList());

        // 构建主流程任务节点映射，便于后续查找
        List<WfProcNodeVo> mainProcessNodes = buildTaskNodeListEnhanced(mainProcessTaskDefs, mainHistoricActivities, allActiveTasks, allComments);
        Map<String, WfProcNodeVo> mainProcessNodeMap = mainProcessNodes.stream()
                .collect(Collectors.toMap(WfProcNodeVo::getActivityId, node -> node, (existing, replacement) -> existing));

        // 获取已执行的活动ID集合（用于已完成流程的过滤）
        Set<String> executedActivityIds = allHistoricActivities.stream()
                .map(HistoricActivityInstance::getActivityId)
                .collect(Collectors.toSet());

        // 严格按照流程定义的顺序遍历FlowElement，保持定义顺序

        // 严格按照流程定义的顺序遍历FlowElement，保持定义顺序
        List<WfProcNodeVo> subProcessNodes = new ArrayList<>();
        for (FlowElement flowElement : flowElements) {
            WfProcNodeVo nodeVo = null;

            if (flowElement instanceof UserTask) {
                // 处理主流程的用户任务节点
                nodeVo = mainProcessNodeMap.get(flowElement.getId());
            } else if (flowElement instanceof SubProcess) {
                // 处理子流程节点 - 只展示定义信息
                nodeVo = processSubProcessNodeDefinitionOnly((SubProcess) flowElement, allHistoricActivities, isProcessCompleted, executedActivityIds);
            } else if (flowElement instanceof CallActivity) {
                // 处理调用子流程节点 - 只展示定义信息
                nodeVo = processCallActivityNodeDefinitionOnly((CallActivity) flowElement, allHistoricActivities, isProcessCompleted, executedActivityIds);
            }

            // 如果是已完成流程，只添加已执行的节点
            if (nodeVo != null) {
                if (!isProcessCompleted || executedActivityIds.contains(flowElement.getId())) {
                    // 区分主流程节点和子流程节点
                    if (flowElement instanceof SubProcess || flowElement instanceof CallActivity) {
                        subProcessNodes.add(nodeVo);
                    } else {
                        procNodeVoList.add(nodeVo);
                    }
                }
            }
        }
        // 将子流程节点添加到主列表的第5个位置
        int insertIndex = Math.min(5, procNodeVoList.size());
        procNodeVoList.addAll(insertIndex, subProcessNodes);

        return procNodeVoList;
    }


    /**
     * 处理子流程节点（简化版本）
     * 只展示简单的流程及实例信息，不展示详细的任务节点
     */
    private WfProcNodeVo processSubProcessNodeSimplified(Activity subProcess,
                                                         HistoricProcessInstance historicProcIns,
                                                         List<HistoricActivityInstance> allHistoricActivities) {
        WfProcNodeVo subProcessNodeVo = new WfProcNodeVo();
        subProcessNodeVo.setActivityId(subProcess.getId());
        subProcessNodeVo.setActivityName(subProcess.getName());
        subProcessNodeVo.setActivityType(BpmnXMLConstants.ELEMENT_SUBPROCESS);

        subProcessNodeVo.setCreateTime(allHistoricActivities.stream()
                .filter(h -> h.getActivityId().equals(subProcess.getId()))
                .map(HistoricActivityInstance::getStartTime)
                .min(Date::compareTo).orElse(null));

        MultiInstanceLoopCharacteristics loopChars = subProcess.getLoopCharacteristics();
        if (loopChars == null || StringUtils.isBlank(loopChars.getElementVariable())) return subProcessNodeVo;
        String elementVariable = loopChars.getElementVariable();

        List<HistoricActivityInstance> subProcessInstances = allHistoricActivities.stream()
                .filter(h -> subProcess.getId().equals(h.getActivityId())).collect(Collectors.toList());
        // 改进 isActive 判断逻辑：子流程已开始且未结束才算活动状态
        subProcessNodeVo.setIsActive(subProcessInstances.stream().anyMatch(h -> h.getStartTime() != null && h.getEndTime() == null));

        if (subProcessInstances.isEmpty()) return subProcessNodeVo;
        
        List<WfSubProcessInstanceVo> subProcessInstanceVos = new ArrayList<>();
        for (HistoricActivityInstance subInstance : subProcessInstances) {
            WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
            vo.setInstanceId(subInstance.getId());
            vo.setCreateTime(subInstance.getStartTime());
            vo.setEndTime(subInstance.getEndTime());
            // 改进 isActive 判断逻辑：子流程实例已开始且未结束才算活动状态
            vo.setIsActive(subInstance.getStartTime() != null && subInstance.getEndTime() == null);

            // 设置父执行ID
            vo.setParentExecutionId(subInstance.getExecutionId());

            HistoricVariableInstance loopVar = historyService.createHistoricVariableInstanceQuery()
                    .executionId(subInstance.getExecutionId()).variableName(elementVariable).singleResult();
            String groupKey = (loopVar != null) ? String.valueOf(loopVar.getValue()) : "unknown_group";
            String deptName = (loopVar != null) ? adapter.getDeptNameById(groupKey) : "未知部门";
            vo.setInstanceName(deptName + " - " + subProcess.getName());

            // 计算耗时
            if (vo.getEndTime() != null) {
                vo.setDuration(DateUtils.getDatePoor(vo.getEndTime(), vo.getCreateTime()));
            } else {
                vo.setDuration(DateUtils.getDatePoor(DateUtils.getNowDate(), vo.getCreateTime()));
            }

            // 不设置详细的任务节点，只保留基本信息
            subProcessInstanceVos.add(vo);
        }
        subProcessNodeVo.setSubProcessInstances(subProcessInstanceVos);
        return subProcessNodeVo;
    }

    /**
     * 处理调用子流程节点（只展示定义信息）
     * 不展示详细的实例信息，减少冗余数据
     */
    private WfProcNodeVo processCallActivityNodeDefinitionOnly(CallActivity callActivity,
                                                               List<HistoricActivityInstance> allHistoricActivities,
                                                               boolean isProcessCompleted,
                                                               Set<String> executedActivityIds) {
        WfProcNodeVo nodeVo = new WfProcNodeVo();
        nodeVo.setActivityId(callActivity.getId());
        nodeVo.setActivityName(callActivity.getName());
        nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY);


        // 查找调用活动的历史实例
        List<HistoricActivityInstance> callActivityHistories = allHistoricActivities.stream()
                .filter(h -> callActivity.getId().equals(h.getActivityId()))
                .collect(Collectors.toList());

        if (!callActivityHistories.isEmpty()) {
            // 设置基本时间信息
            nodeVo.setCreateTime(callActivityHistories.stream()
                    .map(HistoricActivityInstance::getStartTime)
                    .min(Date::compareTo).orElse(null));

            // 判断整体状态
            boolean allCompleted = callActivityHistories.stream().allMatch(h -> h.getEndTime() != null);
            boolean anyStarted = callActivityHistories.stream().anyMatch(h -> h.getStartTime() != null);

            if (allCompleted) {
                nodeVo.setIsActive(false);
                nodeVo.setEndTime(callActivityHistories.stream()
                        .map(HistoricActivityInstance::getEndTime)
                        .filter(Objects::nonNull)
                        .max(Date::compareTo).orElse(null));
            } else if (anyStarted) {
                nodeVo.setIsActive(true);
            } else {
                nodeVo.setIsActive(false);
            }

            // 只设置基本的实例数量信息，不包含详细的运行时数据
            nodeVo.setSubProcessInstanceCount(callActivityHistories.size());
        } else {
            nodeVo.setIsActive(false);
            nodeVo.setSubProcessInstanceCount(0);
        }

        return nodeVo;
    }

    /**
     * 处理子流程节点（只展示定义信息）
     * 不展示详细的实例信息，减少冗余数据
     */
    private WfProcNodeVo processSubProcessNodeDefinitionOnly(SubProcess subProcess,
                                                             List<HistoricActivityInstance> allHistoricActivities,
                                                             boolean isProcessCompleted,
                                                             Set<String> executedActivityIds) {
        WfProcNodeVo nodeVo = new WfProcNodeVo();
        nodeVo.setActivityId(subProcess.getId());
        nodeVo.setActivityName(subProcess.getName());
        nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_SUBPROCESS);

        // 查找子流程的历史实例
        List<HistoricActivityInstance> subProcessHistories = allHistoricActivities.stream()
                .filter(h -> subProcess.getId().equals(h.getActivityId()))
                .collect(Collectors.toList());

        if (!subProcessHistories.isEmpty()) {
            // 设置基本时间信息
            nodeVo.setCreateTime(subProcessHistories.stream()
                    .map(HistoricActivityInstance::getStartTime)
                    .min(Date::compareTo).orElse(null));

            // 判断整体状态
            boolean allCompleted = subProcessHistories.stream().allMatch(h -> h.getEndTime() != null);
            boolean anyStarted = subProcessHistories.stream().anyMatch(h -> h.getStartTime() != null);

            if (allCompleted) {
                nodeVo.setIsActive(false);
                nodeVo.setEndTime(subProcessHistories.stream()
                        .map(HistoricActivityInstance::getEndTime)
                        .filter(Objects::nonNull)
                        .max(Date::compareTo).orElse(null));
            } else if (anyStarted) {
                nodeVo.setIsActive(true);
            } else {
                nodeVo.setIsActive(false);
            }

            // 只设置基本的实例数量信息，不包含详细的运行时数据
            nodeVo.setSubProcessInstanceCount(subProcessHistories.size());
        } else {
            nodeVo.setIsActive(false);
            nodeVo.setSubProcessInstanceCount(0);
        }

        return nodeVo;
    }

    /**
     * 处理多实例子流程节点（完整版本）
     * 此版本确保将正确的历史任务、活动任务和【所有评论】传递给节点构建器。
     */
    private WfProcNodeVo processMultiInstanceSubProcessNodeOptimized(SubProcess subProcess,
                                                                     HistoricProcessInstance historicProcIns,
                                                                     List<HistoricActivityInstance> allHistoricActivities,
                                                                     List<Task> allActiveTasks,
                                                                     List<Comment> allComments) {
        WfProcNodeVo subProcessNodeVo = new WfProcNodeVo();
        subProcessNodeVo.setActivityId(subProcess.getId());
        subProcessNodeVo.setActivityName(subProcess.getName());
        subProcessNodeVo.setActivityType(BpmnXMLConstants.ELEMENT_SUBPROCESS);

        subProcessNodeVo.setCreateTime(allHistoricActivities.stream()
                .filter(h -> h.getActivityId().equals(subProcess.getId()))
                .map(HistoricActivityInstance::getStartTime)
                .min(Date::compareTo).orElse(null));

        MultiInstanceLoopCharacteristics loopChars = subProcess.getLoopCharacteristics();
        if (loopChars == null || StringUtils.isBlank(loopChars.getElementVariable())) return subProcessNodeVo;
        String elementVariable = loopChars.getElementVariable();

        List<HistoricActivityInstance> subProcessInstances = allHistoricActivities.stream()
                .filter(h -> subProcess.getId().equals(h.getActivityId())).collect(Collectors.toList());

        if (subProcessInstances.isEmpty()) return subProcessNodeVo;

        // 改进 isActive 判断逻辑：子流程已开始且未结束才算活动状态
        subProcessNodeVo.setIsActive(subProcessInstances.stream().anyMatch(h -> h.getStartTime() != null && h.getEndTime() == null));

        // 预处理：按 elementVariable 对子流程内部所有任务进行分组
        Set<String> innerTaskKeys = ModelUtils.getAllUserTaskEvent(subProcess.getFlowElements(), new ArrayList<>())
                .stream().map(UserTask::getId).collect(Collectors.toSet());

        Map<String, List<HistoricActivityInstance>> historicTasksGroupedByVar = allHistoricActivities.stream()
                .filter(h -> innerTaskKeys.contains(h.getActivityId()))
                .collect(Collectors.groupingBy(taskInstance -> {
                    HistoricVariableInstance var = historyService.createHistoricVariableInstanceQuery()
                            .executionId(taskInstance.getExecutionId()).variableName(elementVariable).singleResult();
                    return var != null ? String.valueOf(var.getValue()) : "unknown_group";
                }));

        Map<String, List<Task>> activeTasksGroupedByVar = allActiveTasks.stream()
                .filter(t -> innerTaskKeys.contains(t.getTaskDefinitionKey()))
                .collect(Collectors.groupingBy(task -> {
                    Object var = runtimeService.getVariableLocal(task.getExecutionId(), elementVariable);
                    return var != null ? String.valueOf(var) : "unknown_group";
                }));

        List<WfSubProcessInstanceVo> subProcessInstanceVos = new ArrayList<>();
        for (HistoricActivityInstance subInstance : subProcessInstances) {
            WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
            vo.setInstanceId(subInstance.getId());
            vo.setCreateTime(subInstance.getStartTime());
            vo.setEndTime(subInstance.getEndTime());
            // 改进 isActive 判断逻辑：子流程实例已开始且未结束才算活动状态
            vo.setIsActive(subInstance.getStartTime() != null && subInstance.getEndTime() == null);

            HistoricVariableInstance loopVar = historyService.createHistoricVariableInstanceQuery()
                    .executionId(subInstance.getExecutionId()).variableName(elementVariable).singleResult();
            String groupKey = (loopVar != null) ? String.valueOf(loopVar.getValue()) : "unknown_group";
            String deptName = (loopVar != null) ? adapter.getDeptNameById(groupKey) : "未知部门";
            vo.setInstanceName(deptName + " - " + subProcess.getName());

            List<HistoricActivityInstance> instanceSpecificHistoricTasks = historicTasksGroupedByVar.getOrDefault(groupKey, Collections.emptyList());
            List<Task> instanceSpecificActiveTasks = activeTasksGroupedByVar.getOrDefault(groupKey, Collections.emptyList());

            // 调用统一构建器，并确保传入【allComments】
            vo.setTaskNodes(buildTaskNodeListEnhanced(
                    ModelUtils.getAllUserTaskEvent(subProcess.getFlowElements(), new ArrayList<>()),
                    instanceSpecificHistoricTasks,
                    instanceSpecificActiveTasks,
                    allComments
            ));
            subProcessInstanceVos.add(vo);
        }
        subProcessNodeVo.setSubProcessInstances(subProcessInstanceVos);
        return subProcessNodeVo;
    }

    /**
     * 增强版用户任务节点列表构建器
     * 支持已完成、进行中、未开始的用户任务，对于进行中的任务展示已处理和未处理的人员信息
     * 优化驳回功能后的评论显示逻辑：当前活动任务评论置空，之后任务评论全部清空，之前任务只保留最新评论
     *
     * @param taskDefinitions 任务定义集合
     * @param relevantHistoricActivities 相关历史活动实例列表
     * @param relevantActiveTasks 相关活动任务列表
     * @param allComments 所有评论
     * @return 用户任务节点列表
     */
    private List<WfProcNodeVo> buildTaskNodeListEnhanced(
            Collection<UserTask> taskDefinitions,
            List<HistoricActivityInstance> relevantHistoricActivities,
            List<Task> relevantActiveTasks,
            List<Comment> allComments) {

        List<WfProcNodeVo> nodeVoList = new ArrayList<>();

        // 性能优化：预先构建Map以避免重复查询
        Map<String, List<HistoricActivityInstance>> historicActivityMap = relevantHistoricActivities.stream()
                .collect(Collectors.groupingBy(HistoricActivityInstance::getActivityId));

        Map<String, List<Task>> activeTaskMap = relevantActiveTasks.stream()
                .collect(Collectors.groupingBy(Task::getTaskDefinitionKey));

        // 性能优化：预先构建评论Map，按taskId分组
        Map<String, List<Comment>> commentsByTaskId = allComments.stream()
                .collect(Collectors.groupingBy(Comment::getTaskId));

        // 确定当前活动任务的activityId集合
        Set<String> currentActiveActivityIds = relevantActiveTasks.stream()
                .map(Task::getTaskDefinitionKey)
                .collect(Collectors.toSet());

        // 按流程定义顺序处理任务，确定任务的相对位置
        List<UserTask> sortedTaskDefs = new ArrayList<>(taskDefinitions);

        // 遍历所有任务定义，以构建完整的节点列表
        for (int i = 0; i < sortedTaskDefs.size(); i++) {
            UserTask taskDef = sortedTaskDefs.get(i);
            String activityId = taskDef.getId();
            List<HistoricActivityInstance> nodeHistoricInstances = historicActivityMap.getOrDefault(activityId, Collections.emptyList());
            List<Task> nodeActiveTasks = activeTaskMap.getOrDefault(activityId, Collections.emptyList());
            
            boolean isCurrentActive = !nodeActiveTasks.isEmpty();
            boolean isAfterCurrentActive = isAfterCurrentActiveTask(activityId, currentActiveActivityIds, sortedTaskDefs, i);

            WfProcNodeVo nodeVo = new WfProcNodeVo();
            nodeVo.setActivityId(activityId);
            nodeVo.setActivityName(taskDef.getName());
            nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
            nodeVo.setIsActive(isCurrentActive);

            if (!nodeHistoricInstances.isEmpty()) {
                // 状态：已完成 (或部分完成，对于多实例任务)
                nodeVo.setCreateTime(nodeHistoricInstances.stream().map(HistoricActivityInstance::getStartTime).min(Date::compareTo).orElse(null));

                // 只有当所有实例都完成时，该节点才算最终完成
                boolean isCompleted = nodeHistoricInstances.stream().allMatch(h -> h.getEndTime() != null);
                if (isCompleted) {
                    nodeVo.setEndTime(nodeHistoricInstances.stream().map(HistoricActivityInstance::getEndTime)
                        .max(Date::compareTo).orElse(null));
                }

                // 聚合所有处理人的ID和姓名
                List<String> assigneeIds = nodeHistoricInstances.stream()
                        .map(HistoricActivityInstance::getAssignee)
                        .filter(StringUtils::isNotBlank).distinct()
                        .collect(Collectors.toList());
                
                String assignees = assigneeIds.stream()
                        .map(adapter::getNickNameById)
                        .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                nodeVo.setAssigneeName(assignees);
                nodeVo.setAssigneeId(String.join(",", assigneeIds));

                // 优化评论处理逻辑：根据任务位置决定评论显示策略
                List<FlowComment> comments = processCommentsForTask(
                    nodeHistoricInstances, commentsByTaskId, isCurrentActive, isAfterCurrentActive);
                nodeVo.setCommentList(comments);

            } else if (isCurrentActive) {
                // 状态：进行中 - 增强处理
                Task activeTask = nodeActiveTasks.get(0);
                nodeVo.setCreateTime(activeTask.getCreateTime());

                // 对于多实例任务，显示最后一批次的处理评论；对于非多实例任务，评论列表置空
                if (nodeHistoricInstances.size() > 1) {
                    // 多实例任务：显示前面批次的处理评论，帮助当前处理人了解处理历史
                    List<FlowComment> comments = processCommentsForTask(
                        nodeHistoricInstances, commentsByTaskId, isCurrentActive, isAfterCurrentActive);
                    nodeVo.setCommentList(comments);
                } else {
                    // 非多实例任务：当前活动任务的评论列表置空
                    nodeVo.setCommentList(new ArrayList<>());
                }

                // 获取已处理的用户信息
                List<String> processedUsers = new ArrayList<>();
                List<String> processedUserIds = new ArrayList<>();
                if (StringUtils.isNotBlank(activeTask.getAssignee())) {
                    processedUsers.add(adapter.getNickNameById(activeTask.getAssignee()));
                    processedUserIds.add(activeTask.getAssignee());
                }

                // 获取候选人信息（未处理的人员）
                String candidateInfo = getCandidateInfoForActiveTask(activeTask);

                // 组合已处理和未处理的信息
                StringBuilder assigneeInfo = new StringBuilder();
                if (!processedUsers.isEmpty()) {
                    assigneeInfo.append("已处理: ").append(String.join(",", processedUsers));
                }
                if (StringUtils.isNotBlank(candidateInfo)) {
                    if (assigneeInfo.length() > 0) {
                        assigneeInfo.append("; ");
                    }
                    assigneeInfo.append("待处理: ").append(candidateInfo);
                }

                if (assigneeInfo.length() > 0) {
                    nodeVo.setAssigneeName(assigneeInfo.toString());
                    // 设置已处理的用户ID
                    if (!processedUserIds.isEmpty()) {
                        nodeVo.setAssigneeId(String.join(",", processedUserIds));
                    }
                } else {
                    nodeVo.setCandidate(getCandidateInfo(taskDef));
                }

            } else {
                // 状态：未开始
                nodeVo.setCandidate(getCandidateInfo(taskDef));
                
                // 未开始任务：如果在当前活动任务之后，评论列表清空，并且开始时间和结束时间也置空
                if (isAfterCurrentActive) {
                    nodeVo.setCommentList(new ArrayList<>());
                    nodeVo.setCreateTime(null);
                    nodeVo.setEndTime(null);
                } else {
                    nodeVo.setCommentList(new ArrayList<>());
                }
            }
            nodeVoList.add(nodeVo);
        }
        // 根据创建时间对节点排序，保证展示顺序的逻辑性
        nodeVoList.sort(Comparator.comparing(WfProcNodeVo::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())));
        return nodeVoList;
    }

    /**
     * 判断指定任务是否在当前活动任务之后
     * 
     * @param activityId 当前检查的任务活动ID
     * @param currentActiveActivityIds 当前活动任务的活动ID集合
     * @param sortedTaskDefs 按流程定义顺序排列的任务定义列表
     * @param currentIndex 当前任务在列表中的索引
     * @return 如果当前任务在活动任务之后返回true，否则返回false
     */
    private boolean isAfterCurrentActiveTask(String activityId, Set<String> currentActiveActivityIds, 
                                            List<UserTask> sortedTaskDefs, int currentIndex) {
        // 如果没有活动任务，则认为都不在活动任务之后
        if (currentActiveActivityIds.isEmpty()) {
            return false;
        }
        
        // 查找最后一个活动任务的索引
        int lastActiveIndex = -1;
        for (int i = 0; i < sortedTaskDefs.size(); i++) {
            if (currentActiveActivityIds.contains(sortedTaskDefs.get(i).getId())) {
                lastActiveIndex = Math.max(lastActiveIndex, i);
            }
        }
        
        // 如果当前任务索引大于最后一个活动任务索引，则认为在活动任务之后
        return currentIndex > lastActiveIndex;
    }

    /**
     * 根据任务状态和位置处理评论显示逻辑
     * 支持多实例任务的最后一批次评论处理
     *
     * @param nodeHistoricInstances 历史活动实例列表
     * @param commentsByTaskId 按任务ID分组的评论Map
     * @param isCurrentActive 是否为当前活动任务
     * @param isAfterCurrentActive 是否在当前活动任务之后
     * @return 处理后的评论列表
     */
    private List<FlowComment> processCommentsForTask(
            List<HistoricActivityInstance> nodeHistoricInstances,
            Map<String, List<Comment>> commentsByTaskId,
            boolean isCurrentActive,
            boolean isAfterCurrentActive) {

        // 当前活动任务之后的任务：评论列表置空
        if (isAfterCurrentActive) {
            return new ArrayList<>();
        }

        // 获取所有任务ID
        List<String> taskIds = nodeHistoricInstances.stream()
                .map(HistoricActivityInstance::getTaskId)
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (taskIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集所有相关评论
        List<Comment> allComments = taskIds.stream()
            .flatMap(taskId -> commentsByTaskId
                .getOrDefault(taskId,
                    taskService.getTaskComments(taskId, org.ahead4.workflow.enums.FlowComment.NORMAL.getType()))
                .stream())
                .sorted(Comparator.comparing(Comment::getTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        if (allComments.isEmpty()) {
            return new ArrayList<>();
        }

        // 判断是否为多实例任务（同一个活动节点有多个历史实例）
        boolean isMultiInstance = nodeHistoricInstances.size() > 1;

        if (!isMultiInstance) {
            // 非多实例任务：只返回最新的一条评论
            Comment latestComment = allComments.get(0);
            return Collections.singletonList(convertCommentToFlowComment(latestComment));
        }

        // 多实例任务：返回最后一批次处理的所有评论
        return getLastBatchCommentsForMultiInstance(nodeHistoricInstances, allComments);
    }

    /**
     * 获取多实例任务最后一批次处理的所有评论
     * 优化版本：基于结束时间识别最后一批次，更适合串行多实例任务
     *
     * @param nodeHistoricInstances 历史活动实例列表
     * @param allComments 所有相关评论列表（已按时间倒序排序）
     * @return 最后一批次的评论列表
     */
    private List<FlowComment> getLastBatchCommentsForMultiInstance(
            List<HistoricActivityInstance> nodeHistoricInstances,
            List<Comment> allComments) {

        // 只考虑已完成的实例（有结束时间的实例），按结束时间倒序排序
        List<HistoricActivityInstance> completedInstances = nodeHistoricInstances.stream()
                .filter(instance -> instance.getEndTime() != null)
                .sorted(Comparator.comparing(HistoricActivityInstance::getEndTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        if (completedInstances.isEmpty()) {
            // 如果没有已完成的实例，返回空列表（当前可能是第一个处理的任务）
            return new ArrayList<>();
        }

        // 获取最新完成实例的结束时间作为基准
        Date latestEndTime = completedInstances.get(0).getEndTime();

        // 找到同一批次的所有实例（在时间窗口内结束的实例）
        List<HistoricActivityInstance> sameBatchInstances = new ArrayList<>();
        for (HistoricActivityInstance instance : completedInstances) {
            Date instanceEndTime = instance.getEndTime();
            if (instanceEndTime != null) {
                long timeDiff = Math.abs(latestEndTime.getTime() - instanceEndTime.getTime());
                if (timeDiff <= MULTI_INSTANCE_BATCH_TIME_WINDOW) {
                    sameBatchInstances.add(instance);
                } else {
                    // 由于按时间倒序排列，一旦超出时间窗口就可以停止
                    break;
                }
            }
        }

        // 如果没有找到同批次的实例，至少返回最新完成的实例
        if (sameBatchInstances.isEmpty()) {
            sameBatchInstances.add(completedInstances.get(0));
        }

        // 获取同一批次实例的任务ID
        Set<String> sameBatchTaskIds = sameBatchInstances.stream()
                .map(HistoricActivityInstance::getTaskId)
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        // 过滤出属于同一批次的评论
        List<Comment> batchComments = allComments.stream()
                .filter(comment -> sameBatchTaskIds.contains(comment.getTaskId()))
                .collect(Collectors.toList());

        // 转换为FlowComment并返回
        return batchComments.stream()
                .map(this::convertCommentToFlowComment)
                .collect(Collectors.toList());
    }

//    /**
//     * 统一的用户任务节点列表构建器（原版本，保持向后兼容）
//     */
//    private List<WfProcNodeVo> buildTaskNodeList(
//            Collection<UserTask> taskDefinitions,
//            List<HistoricActivityInstance> relevantHistoricActivities,
//            List<Task> relevantActiveTasks,
//            List<Comment> allComments) {
//
//        List<WfProcNodeVo> nodeVoList = new ArrayList<>();
//
//        // 为了高效查询，将列表转换为Map
//        Map<String, List<HistoricActivityInstance>> historicActivityMap = relevantHistoricActivities.stream()
//                .collect(Collectors.groupingBy(HistoricActivityInstance::getActivityId));
//
//        Map<String, List<Task>> activeTaskMap = relevantActiveTasks.stream()
//                .collect(Collectors.groupingBy(Task::getTaskDefinitionKey));
//
//        // 遍历所有任务定义，以构建完整的节点列表
//        for (UserTask taskDef : taskDefinitions) {
//            String activityId = taskDef.getId();
//            List<HistoricActivityInstance> nodeHistoricInstances = historicActivityMap.getOrDefault(activityId, Collections.emptyList());
//            List<Task> nodeActiveTasks = activeTaskMap.getOrDefault(activityId, Collections.emptyList());
//
//            WfProcNodeVo nodeVo = new WfProcNodeVo();
//            nodeVo.setActivityId(activityId);
//            nodeVo.setActivityName(taskDef.getName());
//            nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
//            nodeVo.setIsActive(!nodeActiveTasks.isEmpty());
//
//            if (!nodeHistoricInstances.isEmpty()) {
//                // 状态：已完成 (或部分完成，对于多实例任务)
//                // 聚合所有实例的信息
//                nodeVo.setCreateTime(nodeHistoricInstances.stream().map(HistoricActivityInstance::getStartTime).min(Date::compareTo).orElse(null));
//
//                // 只有当所有实例都完成时，该节点才算最终完成
//                boolean isCompleted = nodeHistoricInstances.stream().allMatch(h -> h.getEndTime() != null);
//                if (isCompleted) {
//                    nodeVo.setEndTime(nodeHistoricInstances.stream().map(HistoricActivityInstance::getEndTime).max(Date::compareTo).orElse(null));
//                }
//
//                // 聚合所有处理人的ID和姓名
//                List<String> assigneeIds = nodeHistoricInstances.stream()
//                        .map(HistoricActivityInstance::getAssignee)
//                        .filter(StringUtils::isNotBlank).distinct()
//                        .collect(Collectors.toList());
//
//                String assignees = assigneeIds.stream()
//                        .map(userId -> adapter.getNickNameById(userId))
//                        .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
//                nodeVo.setAssigneeName(assignees);
//                nodeVo.setAssigneeId(String.join(",", assigneeIds));
//
//                // 聚合所有评论
//                List<String> taskIds = nodeHistoricInstances.stream().map(HistoricActivityInstance::getTaskId).collect(Collectors.toList());
//                List<FlowComment> comments = allComments.stream()
//                        .filter(c -> taskIds.contains(c.getTaskId()))
//                        .map(this::convertCommentToFlowComment).collect(Collectors.toList());
//                nodeVo.setCommentList(comments);
//
//            } else if (!nodeActiveTasks.isEmpty()) {
//                // 状态：进行中
//                Task activeTask = nodeActiveTasks.get(0);
//                nodeVo.setCreateTime(activeTask.getCreateTime());
//                if (StringUtils.isNotBlank(activeTask.getAssignee())) {
//                    nodeVo.setAssigneeName(adapter.getNickNameById(activeTask.getAssignee()));
//                } else {
//                    // 任务是活动的但尚未签收，显示候选人信息
//                    nodeVo.setCandidate(getCandidateInfo(taskDef));
//                }
//            } else {
//                // 状态：未开始
//                // 节点既没有历史记录，也不是活动任务，因此是待办状态
//                nodeVo.setCandidate(getCandidateInfo(taskDef));
//            }
//            nodeVoList.add(nodeVo);
//        }
//        // 根据创建时间对节点排序，保证展示顺序的逻辑性
//        nodeVoList.sort(Comparator.comparing(WfProcNodeVo::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())));
//        return nodeVoList;
//    }


    /**
     * Helper to convert an engine Comment to our DTO.
     */
    private FlowComment convertCommentToFlowComment(Comment comment) {
        FlowComment flowComment = new FlowComment();
        BeanUtil.copyProperties(comment, flowComment);
        String userId = comment.getUserId();
        if (StringUtils.isNotBlank(userId)) {
            // Enhance with user details from your system
            String nickName = adapter.getNickNameById(userId);
            flowComment.setDisplayname(nickName);
            // You can add department info here if your adapter supports it
        }
        return flowComment;
    }

    /**
     * Helper to get candidate user/group information from a UserTask definition.
     */
    private String getCandidateInfo(UserTask userTask) {
        Set<String> candidates = new LinkedHashSet<>();
        // Logic from your original `buildPendingTaskNode` can be reused here
        // to get and format candidate users and groups.
        if (CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
            for (String userId : userTask.getCandidateUsers()) {
                candidates.add(adapter.getNickNameById(userId));
            }
        }
        if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
            for (String groupId : userTask.getCandidateGroups()) {
                if (groupId.startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
                    String roleId = StringUtils.stripStart(groupId, TaskConstants.ROLE_GROUP_PREFIX);
                    candidates.add(adapter.getRoleNameById(roleId));
                } else if (groupId.startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
                    String deptId = StringUtils.stripStart(groupId, TaskConstants.DEPT_GROUP_PREFIX);
                    candidates.add(adapter.getDeptNameById(deptId));
                }
            }
        }
        return String.join(",", candidates);
    }

//    /**
//     * 获取活动任务的候选人信息（用于进行中任务的未处理人员显示）
//     *
//     * @param activeTask 活动任务
//     * @return 候选人信息字符串
//     */
//    private String getCandidateInfoForActiveTask(Task activeTask) {
//        Set<String> candidates = new LinkedHashSet<>();
//
//        // 获取候选用户和组
//        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(activeTask.getId());
//        for (IdentityLink identityLink : identityLinks) {
//            if (IdentityLinkType.CANDIDATE.equals(identityLink.getType())) {
//                if (StringUtils.isNotBlank(identityLink.getUserId())) {
//                    String nickName = adapter.getNickNameById(identityLink.getUserId());
//                    if (StringUtils.isNotBlank(nickName)) {
//                        candidates.add(nickName);
//                    }
//                } else if (StringUtils.isNotBlank(identityLink.getGroupId())) {
//                    String groupId = identityLink.getGroupId();
//                    if (groupId.startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
//                        String roleId = StringUtils.stripStart(groupId, TaskConstants.ROLE_GROUP_PREFIX);
//                        candidates.add(adapter.getRoleNameById(roleId));
//                    } else if (groupId.startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
//                        String deptId = StringUtils.stripStart(groupId, TaskConstants.DEPT_GROUP_PREFIX);
//                        candidates.add(adapter.getDeptNameById(deptId));
//                    } else {
//                        candidates.add(groupId);
//                    }
//                }
//            }
//        }
//
//        return String.join(",", candidates);
//    }

    /**
     * 分页查询子流程实例信息（优化版本）
     * 为运行中的子流程实例增加待办任务列表
     *
     * @param procInstId            流程实例ID
     * @param activityId            子流程执ID
     * @param pageQuery             分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<WfSubProcessInstanceVo> selectPageSubProcessInstances(String procInstId, String activityId, PageQuery pageQuery) {
        // 参数验证
        if (StringUtils.isBlank(procInstId)) {
            throw new RestException("流程实例ID不能为空");
        }
        if (StringUtils.isBlank(activityId)) {
            throw new RestException("活动ID不能为空");
        }

        // 1. 创建对多实例子流程活动的历史查询
        HistoricActivityInstanceQuery query = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId) // 按主流程实例ID查询
                .activityId(activityId)       // 按多实例子流程的活动ID查询
                .orderByHistoricActivityInstanceStartTime().asc(); // 按开始时间排序

        // 2. 获取总数用于分页
        long total = query.count();
        if (total == 0) {
            return TableDataInfo.build();
        }

        // 3. 执行分页查询
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<HistoricActivityInstance> subProcessInstances = query.listPage(offset, pageQuery.getPageSize());

        // 4. 遍历每个子流程迭代实例，转换为VO对象
        List<WfSubProcessInstanceVo> resultList = new ArrayList<>();
        List<String> runningInstanceExecutionIds = new ArrayList<>();

        for (HistoricActivityInstance instance : subProcessInstances) {
            WfSubProcessInstanceVo vo = buildSubProcessInstanceVo(instance, procInstId);
            resultList.add(vo);

            // 收集运行中的实例执行ID，用于批量查询待办任务
            if (vo.getIsActive()) {
                runningInstanceExecutionIds.add(instance.getCalledProcessInstanceId());
            }
        }

        // 5. 批量查询运行中子流程实例的待办任务
        if (!runningInstanceExecutionIds.isEmpty()) {
            Map<String, List<WfTaskVo>> pendingTasksMap = batchQueryPendingTasksForSubProcessInstances(runningInstanceExecutionIds);

            // 为运行中的子流程实例附加待办任务列表
            for (WfSubProcessInstanceVo vo : resultList) {
                if (vo.getIsActive() && pendingTasksMap.containsKey(vo.getProcessInstanceId())) {
                    vo.setPendingTasks(pendingTasksMap.get(vo.getProcessInstanceId()));
                }
            }
        }

        // 6. 构建并返回分页结果
        TableDataInfo<WfSubProcessInstanceVo> dataInfo = new TableDataInfo<>();
        dataInfo.setTotal(total);
        dataInfo.setRows(resultList);
        return dataInfo;
    }

    /**
     * 构建子流程实例VO对象
     */
    private WfSubProcessInstanceVo buildSubProcessInstanceVo(HistoricActivityInstance instance, String mainProcessInstanceId) {
        WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
        vo.setInstanceId(instance.getId());
        vo.setCreateTime(instance.getStartTime());
        vo.setStartTime(instance.getStartTime());
        vo.setEndTime(instance.getEndTime());
        vo.setExecutionId(instance.getCalledProcessInstanceId());
        vo.setIsActive(instance.getEndTime() == null);

        // 设置主流程实例ID和子流程实例ID
        vo.setProcessInstanceId(instance.getCalledProcessInstanceId()); // 子流程实例ID
        vo.setParentExecutionId(mainProcessInstanceId); // 主流程实例ID

        String deptCode;
        String deptName = null;
        // 获取并设置循环变量（如部门信息）
        HistoricVariableInstance deptVar = historyService.createHistoricVariableInstanceQuery()
                .executionId(instance.getExecutionId())
                .variableName("deptLeader")
                .singleResult();
        if (deptVar != null && deptVar.getValue() != null) {
            // 如果是CCDto对象，提取部门信息
            CCDto value = (CCDto) deptVar.getValue();
            deptCode = value.getDeptCode();
            deptName = Optional.ofNullable(value.getDeptName()).orElseGet(() -> adapter.getDeptNameById(deptCode));
        } else {
            deptCode = null;
        }

        if (StringUtils.isNotBlank(deptCode)) {
            vo.setDeptCode(deptCode);
            vo.setDeptName(deptName);
            vo.setInstanceName(deptName);
        } else {
            vo.setInstanceName(deptName);
        }

        // 计算耗时
        if (instance.getStartTime() != null) {
            long durationMillis = (instance.getEndTime() != null)
                    ? (instance.getEndTime().getTime() - instance.getStartTime().getTime())
                    : (System.currentTimeMillis() - instance.getStartTime().getTime());
            vo.setDuration(DateUtil.formatBetween(durationMillis, BetweenFormatter.Level.SECOND));
        }

        // 根据实例状态（活动/已完成），获取任务处理人信息
        if (vo.getIsActive()) {
            // 如果实例是活动的，查找当前待办任务
            handleActiveSubProcessInstance(instance, vo);
        } else {
            // 如果实例已完成，查找最后一个完成的任务
            handleCompletedSubProcessInstance(instance, vo);
        }

        return vo;
    }

    /**
     * 批量查询子流程实例的待办任务，避免N+1查询问题
     */
    private Map<String, List<WfTaskVo>> batchQueryPendingTasksForSubProcessInstances(List<String> processInstanceIds) {
        if (processInstanceIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, List<WfTaskVo>> pendingTasksMap = new HashMap<>();

        // 为每个子流程实例ID查询相关的待办任务
        for (String processInstanceId : processInstanceIds) {
            if (StringUtils.isBlank(processInstanceId)) {
                continue;
            }

            List<WfTaskVo> taskVos = new ArrayList<>();

            // 直接查询子流程实例的活动任务
            List<Task> tasks = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .active()
                    .list();

            for (Task task : tasks) {
                WfTaskVo taskVo = convertToWfTaskVo(task);
                taskVos.add(taskVo);
            }

            if (!taskVos.isEmpty()) {
                pendingTasksMap.put(processInstanceId, taskVos);
            }
        }

        return pendingTasksMap;
    }

    /**
     * 转换Task为WfTaskVo
     */
    private WfTaskVo convertToWfTaskVo(Task task) {
        WfTaskVo taskVo = new WfTaskVo();
        taskVo.setTaskId(task.getId());
        taskVo.setTaskName(task.getName());
        taskVo.setTaskDefKey(task.getTaskDefinitionKey());
        taskVo.setAssigneeId(task.getAssignee());
        taskVo.setCreateTime(task.getCreateTime());
        taskVo.setProcInsId(task.getProcessInstanceId());

        // 设置处理人信息
        if (StringUtils.isNotBlank(task.getAssignee())) {
            taskVo.setAssigneeName(adapter.getNickNameById(task.getAssignee()));
        } else {
            // 获取候选人信息
            taskVo.setAssigneeName(getCandidateInfoForActiveTask(task));
        }

        // 添加任务跳转链接支持
        taskVo.setTaskUrl("/workflow/task/detail/" + task.getId());

        return taskVo;
    }
//        List<HistoricActivityInstance> subProcessInstances = activityDeptSubprocess
//                .listPage(pageQuery.getPageSize() * (pageQuery.getPageNum() - 1), pageQuery.getPageSize());
//
//
//        List<ProcessInstance> list = runtimeService.createProcessInstanceQuery()
//                .excludeSubprocesses(true)
//                .superProcessInstanceId(procInstId)
//                .list();
//
//        List<WfSubProcessInstanceVo> result = new ArrayList<>();
//        for (HistoricActivityInstance instance : subProcessInstances) {
//            WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
//            vo.setInstanceId(instance.getId());
//            vo.setInstanceName(instance.getActivityName());
//            vo.setCreateTime(instance.getStartTime());
//            vo.setStartTime(instance.getStartTime());
//            vo.setEndTime(instance.getEndTime());
//            vo.setIsActive(instance.getEndTime() == null);
//            // 设置执行ID
//            vo.setExecutionId(instance.getExecutionId());
//
//            // 获取部门信息（通过多实例变量）
//            HistoricVariableInstance deptVar = historyService.createHistoricVariableInstanceQuery()
//                    .executionId(instance.getExecutionId())
//                    .variableName("dept") // 对应 <multiInstanceLoopCharacteristics flowable:elementVariable="dept">
//                    .singleResult();
//
//            if (deptVar != null) {
//                String deptCode = deptVar.getValue().toString();
//                vo.setDeptCode(deptCode);
//                vo.setDeptName(adapter.getDeptNameById(deptCode));
//            }
//
//            // 计算耗时
//            if (instance.getStartTime() != null) {
//                long duration = instance.getEndTime() != null ?
//                        instance.getEndTime().getTime() - instance.getStartTime().getTime() :
//                        System.currentTimeMillis() - instance.getStartTime().getTime();
//                vo.setDuration(DateUtils.formatBetween(duration, BetweenFormatter.Level.SECOND));
//            }
//
//            // 根据子流程实例状态处理任务信息
//            if (instance.getEndTime() == null) {
//                // 进行中的子流程实例：获取当前进行中的用户任务及待处理用户信息
//                handleActiveSubProcessInstance(instance, vo);
//            } else {
//                // 已完成的子流程实例：获取最后一个用户任务的处理信息
//                handleCompletedSubProcessInstance(instance, vo);
//            }
//
//            result.add(vo);
//        }
//
//        TableDataInfo<WfSubProcessInstanceVo> build = TableDataInfo.build();
//        build.setTotal(total);
//        build.setRows(result);
//
//        return build;

//    /**
//     * 处理进行中的子流程实例信息
//     * 获取当前进行中的用户任务及待处理用户信息
//     *
//     * @param instance 子流程实例
//     * @param vo       视图对象
//     */
//    private void handleActiveSubProcessInstance(HistoricActivityInstance instance, WfSubProcessInstanceVo vo) {
//        // 递归查找所有相关的执行ID（包括嵌套的多实例）
//        Set<String> allExecutionIds = new HashSet<>();
//        collectAllSubExecutionIds(instance.getExecutionId(), allExecutionIds);
//
//        // 在所有相关执行中查找活动任务
//        for (String executionId : allExecutionIds) {
//            List<Task> activeTasks = taskService.createTaskQuery()
//                    .executionId(executionId)
//                    .active()
//                    .list();
//
//            if (!activeTasks.isEmpty()) {
//                Task activeTask = activeTasks.get(0);
//                setTaskHandlerInfo(activeTask, vo);
//                return; // 找到第一个活动任务即可
//            }
//        }
//    }

    /**
     * 处理进行中的子流程实例，查找其内部所有（包括嵌套的）当前活动任务及处理人信息。
     *
     * @param instance 子流程的历史活动实例
     * @param vo       用于填充数据的视图对象
     */
    private void handleActiveSubProcessInstance(HistoricActivityInstance instance, WfSubProcessInstanceVo vo) {
        if (instance.getActivityType().equals(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY)) {
            String calledProcessInstanceId = instance.getCalledProcessInstanceId();
            taskService.createTaskQuery()
                    .processInstanceId(calledProcessInstanceId)
                    .active()
                    .list()
                    .stream()
                    .findFirst()
                    .ifPresent(task -> setTaskHandlerInfo(task, vo));

        } else if (instance.getActivityType().equals(BpmnXMLConstants.ELEMENT_SUBPROCESS)) {
            // 创建一个集合来存储所有相关的Execution ID
            Set<String> allExecutionIds = new HashSet<>();
            // 递归收集此迭代实例下的所有子Execution ID
            collectAllSubExecutionIds(instance.getExecutionId(), allExecutionIds); //

            if (allExecutionIds.isEmpty()) {
                return;
            }

            // 使用收集到的所有Execution ID，一次性查询出所有相关的活动任务
            // 由于没有 executionIdIn 方法，我们必须遍历ID集合并单独查询
            List<Task> activeTasks = new ArrayList<>();
            for (String executionId : allExecutionIds) {
                List<Task> tasksForExecution = taskService.createTaskQuery()
                        .executionId(executionId)
                        .active()
                        .list();
                if (!tasksForExecution.isEmpty()) {
                    activeTasks.addAll(tasksForExecution);
                }
            }

            if (!activeTasks.isEmpty()) {
                // 将所有活动任务的处理人信息拼接起来
                // 将所有活动任务的ID、名称和处理人信息拼接起来
                // 收集任务ID、处理人ID和姓名信息
                String taskIds = activeTasks.stream()
                        .map(Task::getId)
                        .collect(Collectors.joining(","));

                String taskNames = activeTasks.stream().map(Task::getName).distinct().collect(Collectors.joining(", "));
                String assigneeIds = activeTasks.stream()
                        .map(Task::getAssignee)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.joining(","));
                String assignees = activeTasks.stream()
                        .map(task -> {
                            if (StringUtils.isNotBlank(task.getAssignee())) {
                                return adapter.getNickNameById(task.getAssignee());
                            } else {
                                // 如果任务未签收，则获取候选人信息
                                return getCandidateInfoForActiveTask(task);
                            }
                        })
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining("; "));

                // 设置当前任务信息，便于调用处理任务接口时直接取用
                vo.setTaskId(taskIds); // 当前任务ID
                vo.setAssigneeId(assigneeIds); // 待处理人ID
                vo.setAssigneeName(assignees); // 待处理人姓名
            }
        }

    }

    /**
     * 递归收集一个父Execution下的所有子孙Execution ID。
     *
     * @param parentExecutionId 父执行ID
     * @param allExecutionIds   用于存储所有收集到的ID的集合
     */
    private void collectAllSubExecutionIds(String parentExecutionId, Set<String> allExecutionIds) {
        allExecutionIds.add(parentExecutionId); //
        // 查询直接子执行
        List<Execution> subExecutions = runtimeService.createExecutionQuery()
                .parentId(parentExecutionId)
                .list(); //
        // 递归地为每个子执行调用此方法
        for (Execution subExecution : subExecutions) {
            collectAllSubExecutionIds(subExecution.getId(), allExecutionIds); //
        }
    }
//    /**
//     * 递归收集所有子执行ID（处理嵌套多实例的情况）
//     *
//     * @param parentExecutionId 父执行ID
//     * @param allExecutionIds   收集所有执行ID的集合
//     */
//    private void collectAllSubExecutionIds(String parentExecutionId, Set<String> allExecutionIds) {
//        // 添加当前执行ID
//        allExecutionIds.add(parentExecutionId);
//
//        // 查询直接子执行
//        List<Execution> subExecutions = runtimeService.createExecutionQuery()
//                .parentId(parentExecutionId)
//                .list();
//
//        // 递归处理每个子执行
//        for (Execution subExecution : subExecutions) {
//            collectAllSubExecutionIds(subExecution.getId(), allExecutionIds);
//        }
//    }
//
//    /**
//     * 处理已完成的子流程实例信息
//     * 获取最后一个用户任务的处理信息
//     *
//     * @param instance 子流程实例
//     * @param vo       视图对象
//     */
//    private void handleCompletedSubProcessInstance(HistoricActivityInstance instance, WfSubProcessInstanceVo vo) {
//        // 查询该子流程实例执行范围内的历史任务，按结束时间倒序
//        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
//                .executionId(instance.getExecutionId())
//                .finished()
//                .orderByHistoricTaskInstanceEndTime().desc()
//                .list();
//
//        // 如果当前执行没有任务，查询子执行的任务
//        if (historicTasks.isEmpty()) {
//            List<HistoricActivityInstance> subActivities = historyService.createHistoricActivityInstanceQuery()
//                    .processInstanceId(instance.getProcessInstanceId())
//                    .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
//                    .finished()
//                    .orderByHistoricActivityInstanceEndTime().desc()
//                    .list();
//
//            for (HistoricActivityInstance activity : subActivities) {
//                // 检查该活动是否属于当前子流程实例的执行范围
//                if (isActivityInSubProcessScope(activity, instance)) {
//                    List<HistoricTaskInstance> taskInstances = historyService.createHistoricTaskInstanceQuery()
//                            .executionId(activity.getExecutionId())
//                            .taskDefinitionKey(activity.getActivityId())
//                            .finished()
//                            .orderByHistoricTaskInstanceEndTime().desc()
//                            .list();
//
//                    if (!taskInstances.isEmpty()) {
//                        HistoricTaskInstance lastTask = taskInstances.get(0);
//                        // 设置任务ID
//                        vo.setTaskId(lastTask.getId());
//                        if (StringUtils.isNotBlank(lastTask.getAssignee())) {
//                            vo.setAssigneeId(lastTask.getAssignee());
//                            vo.setAssigneeName(adapter.getNickNameById(lastTask.getAssignee()));
//                        }
//                        break;
//                    }
//                }
//            }
//        } else {
//            // 找到最后一个用户任务
//            HistoricTaskInstance lastTask = historicTasks.get(0);
//            // 设置任务ID
//            vo.setTaskId(lastTask.getId());
//            if (StringUtils.isNotBlank(lastTask.getAssignee())) {
//                vo.setAssigneeId(lastTask.getAssignee());
//                vo.setAssigneeName(adapter.getNickNameById(lastTask.getAssignee()));
//            }
//        }
//    }

    /**
     * 处理已完成的子流程实例，查找其最后一个完成的用户任务的处理人信息。
     * 展示最后一个用户节点的执行信息，包括处理人、时间、部门等。
     *
     * @param instance 子流程的历史活动实例
     * @param vo       用于填充数据的视图对象
     */
    private void handleCompletedSubProcessInstance(HistoricActivityInstance instance, WfSubProcessInstanceVo vo) {
        Date startTime = instance.getStartTime();
        Date endTime = instance.getEndTime();

        if (startTime == null || endTime == null) {
            return;
        }

        // 查询子流程实例中最后完成的用户任务
        List<HistoricTaskInstance> tasksInWindow = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(instance.getCalledProcessInstanceId())
                .finished()
                .taskCompletedAfter(startTime)
                .taskCompletedBefore(endTime)
                .orderByHistoricTaskInstanceEndTime().desc()
                .listPage(0, 1);

        if (!tasksInWindow.isEmpty()) {
            HistoricTaskInstance lastTask = tasksInWindow.get(0);
            // 设置最后一个用户节点的执行信息
            vo.setTaskId(lastTask.getId()); // 最后任务ID
            if (StringUtils.isNotBlank(lastTask.getAssignee())) {
                vo.setAssigneeId(lastTask.getAssignee()); // 最后处理人ID
                vo.setAssigneeName(adapter.getNickNameById(lastTask.getAssignee())); // 最后处理人姓名
            }
            // 结束时间已在buildSubProcessInstanceVo中设置，包含了时间信息
            // 部门信息已在buildSubProcessInstanceVo中从流程变量获取
        }
    }


    /**
     * 获取一个活动任务的候选人/组信息，并格式化为字符串。
     *
     * @param activeTask 活动任务对象
     * @return 格式化后的候选人/组字符串
     */
    private String getCandidateInfoForActiveTask(Task activeTask) {
        Set<String> candidates = new LinkedHashSet<>();
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(activeTask.getId()); //

        for (IdentityLink link : identityLinks) {
            if (IdentityLinkType.CANDIDATE.equals(link.getType())) { //
                if (StringUtils.isNotBlank(link.getUserId())) {
                    String nickName = adapter.getNickNameById(link.getUserId()); //
                    if (StringUtils.isNotBlank(nickName)) {
                        candidates.add(nickName);
                    }
                } else if (StringUtils.isNotBlank(link.getGroupId())) {
                    String groupId = link.getGroupId(); //
                    if (groupId.startsWith(TaskConstants.ROLE_GROUP_PREFIX)) {
                        String roleId = StringUtils.stripStart(groupId, TaskConstants.ROLE_GROUP_PREFIX);
                        candidates.add(adapter.getRoleNameById(roleId)); //
                    } else if (groupId.startsWith(TaskConstants.DEPT_GROUP_PREFIX)) {
                        String deptId = StringUtils.stripStart(groupId, TaskConstants.DEPT_GROUP_PREFIX);
                        candidates.add(adapter.getDeptNameById(deptId)); //
                    } else {
                        candidates.add(groupId);
                    }
                }
            }
        }
        return String.join(",", candidates);
    }

    /**
     * 检查活动是否属于子流程实例的执行范围
     *
     * @param activity           历史活动实例
     * @param subProcessInstance 子流程实例
     * @return 是否属于该子流程实例
     */
    private boolean isActivityInSubProcessScope(HistoricActivityInstance activity, HistoricActivityInstance subProcessInstance) {
        // 通过执行ID的层级关系判断
        String activityExecutionId = activity.getExecutionId();
        String subProcessExecutionId = subProcessInstance.getExecutionId();

        // 如果执行ID相同，直接属于
        if (activityExecutionId.equals(subProcessExecutionId)) {
            return true;
        }

        // 查询执行的父子关系
        List<Execution> executions = runtimeService.createExecutionQuery()
                .processInstanceId(subProcessInstance.getProcessInstanceId())
                .list();

        // 构建执行ID的父子关系映射
        Map<String, String> parentExecutionMap = new HashMap<>();
        for (Execution execution : executions) {
            if (execution.getParentId() != null) {
                parentExecutionMap.put(execution.getId(), execution.getParentId());
            }
        }

        // 向上查找父执行，看是否能找到子流程执行ID
        String currentExecutionId = activityExecutionId;
        while (currentExecutionId != null) {
            if (currentExecutionId.equals(subProcessExecutionId)) {
                return true;
            }
            currentExecutionId = parentExecutionMap.get(currentExecutionId);
        }

        return false;
    }

    /**
     * 设置任务处理人信息
     *
     * @param task 任务
     * @param vo   视图对象
     */
    private void setTaskHandlerInfo(Task task, WfSubProcessInstanceVo vo) {
        // 设置任务ID
        vo.setTaskId(task.getId());

        if (StringUtils.isNotBlank(task.getAssignee())) {
            // 已分配给具体用户
            vo.setAssigneeId(task.getAssignee());
            vo.setAssigneeName(adapter.getNickNameById(task.getAssignee()));
        } else {
            // 获取候选用户和组信息
            vo.setAssigneeName(getCandidateInfoForActiveTask(task));
        }
    }
//
//    /**
//     * 查询子流程实例详细信息（带任务详情）
//     *
//     * @param procInstId        主流程实例ID
//     * @param parentExecutionId 子流程实例ID
//     * @return 子流程实例详细信息（含任务详情）
//     */
//    @Override
//    public WfSubProcessInstanceVo querySubProcessInstanceDetails(String procInstId, String parentExecutionId) {
//        // 1. 获取子流程的执行信息
//        ProcessInstance parentProcessInstance = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(procInstId)
//                .singleResult();
//
//        // To get subprocess instances:
//        List<ProcessInstance> subProcessInstances = runtimeService.createProcessInstanceQuery()
//                .superProcessInstanceId(parentProcessInstance.getId())
//                .list();
//
//        subProcessInstances.forEach(System.out::println);
//
//        subProcessInstances.forEach(sub -> {
//            System.out.println(sub.getProcessDefinitionId());
//            List<Task> tasksInSubprocess = taskService.createTaskQuery()
//                    .processInstanceId(parentExecutionId)
//                    .list();
//
//        });
//
//
//        Execution execution = runtimeService.createExecutionQuery()
//                .processInstanceId(procInstId)
//                .executionId(parentExecutionId)
//                .singleResult();
//
//        HistoricProcessInstance subProcessInstance = null;
//        String actualProcessInstanceId;
//
//        if (execution != null) {
//            actualProcessInstanceId = execution.getProcessInstanceId();
//        } else {
//            List<HistoricActivityInstance> subProcessActivities = historyService.createHistoricActivityInstanceQuery()
//                    .executionId(parentExecutionId)
//                    .list();
//            if (!subProcessActivities.isEmpty()) {
//                actualProcessInstanceId = subProcessActivities.get(0).getProcessInstanceId();
//            } else {
//                actualProcessInstanceId = parentExecutionId;
//            }
//        }
//
//        subProcessInstance = historyService.createHistoricProcessInstanceQuery()
//                .processInstanceId(actualProcessInstanceId)
//                .singleResult();
//
//        if (subProcessInstance == null) {
//            throw new RestException("主流程实例不存在");
//        }
//
//        // 2. 获取子流程的 BPMN 模型
//        BpmnModel bpmnModel = repositoryService.getBpmnModel(subProcessInstance.getProcessDefinitionId());
//
//        // 3. 查询当前流程下所有已完成的任务
//        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
//                .processInstanceId(procInstId)
//                .finished()
//                .list();
//
//        // 4. 查询当前流程下所有在进行中的任务
//        List<Task> activeTasks = taskService.createTaskQuery()
//                .processInstanceId(actualProcessInstanceId)
//                .active()
//                .list();
//
//        // 5. 构建任务节点信息
//        List<WfProcNodeVo> taskNodes = new ArrayList<>();
////
//        for (UserTask userTask : ModelUtils.getAllUserTaskEvent(bpmnModel, execution.getActivityId())) {
//            String taskId = userTask.getId();
//            String taskName = userTask.getName();
//
//            WfProcNodeVo nodeVo = new WfProcNodeVo();
//            nodeVo.setActivityId(taskId);
//            nodeVo.setActivityName(taskName);
//            nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
//
//            // 查找是否为已完成任务
//            HistoricTaskInstance finishedTask = historicTasks.stream()
//                    .filter(t -> t.getTaskDefinitionKey().equals(taskId))
//                    .findFirst()
//                    .orElse(null);
//
//            if (finishedTask != null) {
//                nodeVo.setIsActive(false);
//                nodeVo.setCreateTime(finishedTask.getStartTime());
//                nodeVo.setEndTime(finishedTask.getEndTime());
//                nodeVo.setAssigneeId(finishedTask.getAssignee());
//                nodeVo.setAssigneeName(adapter.getNickNameById(finishedTask.getAssignee()));
//            } else {
//                // 查找是否为进行中的任务
//                Task activeTask = activeTasks.stream()
//                        .filter(t -> t.getTaskDefinitionKey().equals(taskId))
//                        .findFirst()
//                        .orElse(null);
//
//                if (activeTask != null) {
//                    nodeVo.setIsActive(true);
//                    nodeVo.setCreateTime(activeTask.getCreateTime());
//                    nodeVo.setAssigneeId(activeTask.getAssignee());
//                    nodeVo.setAssigneeName(adapter.getNickNameById(activeTask.getAssignee()));
//                } else {
//                    // 未开始的任务
//                    nodeVo.setIsActive(false);
//                    nodeVo.setAssigneeName("未开始");
//                }
//            }
//
//            taskNodes.add(nodeVo);
//        }
//
//        // 6. 构建返回值
//        WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
//        vo.setInstanceId(subProcessInstance.getId());
//        vo.setInstanceName(subProcessInstance.getProcessDefinitionName());
//        vo.setStartTime(subProcessInstance.getStartTime());
//        vo.setEndTime(subProcessInstance.getEndTime());
//        vo.setIsActive(subProcessInstance.getEndTime() == null);
//        vo.setExecutionId(parentExecutionId);
//        vo.setTaskNodes(taskNodes);
//
//        return vo;
//    }

    /**
     * 查询单个多实例子流程迭代的详细信息，包括其内部所有任务的状态。
     *
     * @param procInstId        主流程实例ID
     * @param parentExecutionId 您想查询的那个迭代实例的Execution ID
     * @return 单个子流程迭代实例的详细视图对象，包含内部任务列表
     */
    @Override
    public WfSubProcessInstanceVo querySubProcessInstanceDetails(String procInstId, String parentExecutionId) {
        // 调用通用方法获取详细的任务节点信息
        List<WfProcNodeVo> taskNodes = queryProcessDetails(procInstId, parentExecutionId);

        // 1. 获取该迭代实例自身的历史活动信息
        HistoricActivityInstance subProcessIteration = historyService.createHistoricActivityInstanceQuery()
                .executionId(parentExecutionId)
                .activityType(BpmnXMLConstants.ELEMENT_SUBPROCESS) // 确保是子流程的实例
                .singleResult();

        if (subProcessIteration == null) {
            throw new RestException("未找到指定的子流程实例，Execution ID: " + parentExecutionId);
        }

        // 2. 获取主流程的BPMN模型
        HistoricProcessInstance mainProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstId).singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(mainProcessInstance.getProcessDefinitionId());

        // 3. 在BPMN模型中找到这个子流程的定义
        SubProcess subProcessDef = (SubProcess) bpmnModel.getFlowElement(subProcessIteration.getActivityId());
        if (subProcessDef == null) {
            throw new RestException("在BPMN模型中未找到子流程定义: " + subProcessIteration.getActivityId());
        }

        // 4. 构建并填充 WfSubProcessInstanceVo 对象
        WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
        vo.setInstanceId(subProcessIteration.getId());
        vo.setExecutionId(parentExecutionId);
        vo.setStartTime(subProcessIteration.getStartTime());
        vo.setEndTime(subProcessIteration.getEndTime());
        vo.setIsActive(subProcessIteration.getEndTime() == null);
        boolean isCompleted = subProcessIteration.getEndTime() != null; // 判断是否完成
        vo.setIsActive(!isCompleted);
        // 计算耗时
        if (vo.getStartTime() != null) {
            long duration = vo.getEndTime() != null ?
                    (vo.getEndTime().getTime() - vo.getStartTime().getTime()) :
                    (System.currentTimeMillis() - vo.getStartTime().getTime());
            vo.setDuration(DateUtil.formatBetween(duration, BetweenFormatter.Level.SECOND));
        }
        // 设置部门等信息
        HistoricVariableInstance loopVar = historyService.createHistoricVariableInstanceQuery()
                .executionId(parentExecutionId).variableName("dept").singleResult();
        if (loopVar != null && loopVar.getValue() != null) {
            String deptCode = loopVar.getValue().toString();
            vo.setDeptCode(deptCode);
            vo.setDeptName(adapter.getDeptNameById(deptCode));
            vo.setInstanceName(adapter.getDeptNameById(deptCode));
        } else {
            vo.setInstanceName(subProcessDef.getName());
        }

        // 5. 设置任务节点列表（使用通用方法的返回值）
        vo.setTaskNodes(taskNodes);

        return vo;
    }

    /**
     * 辅助方法：查询一个特定子流程迭代实例中的所有历史任务。
     *
     * @param subProcessIteration 子流程迭代实例的历史活动对象
     * @return 该迭代实例内的所有历史任务列表
     */
    private List<HistoricTaskInstance> findHistoricTasksForIteration(HistoricActivityInstance subProcessIteration) {
        Date startTime = subProcessIteration.getStartTime();
        // 如果实例尚未结束，则使用当前时间作为结束边界
        Date endTime = subProcessIteration.getEndTime() != null ? subProcessIteration.getEndTime() : new Date();

        if (startTime == null) {
            return Collections.emptyList();
        }

        // 在时间窗口内查询所有可能的任务
        List<HistoricTaskInstance> tasksInWindow = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(subProcessIteration.getProcessInstanceId())
                .finished()
                .taskCompletedAfter(startTime)
                .taskCompletedBefore(endTime)
                .includeTaskLocalVariables()
                .list();

        return tasksInWindow;
    }


    /**
     * 辅助方法：构建任务节点列表，整合了任务定义、历史任务和活动任务的信息。
     *
     * @param taskDefinitions 子流程内部的所有用户任务定义
     * @param historicTasks   与此迭代相关的所有历史任务
     * @param activeTasks     与此迭代相关的所有活动任务
     * @param allComments     流程实例的所有评论
     * @return 包含所有任务状态的节点视图列表
     */
    private List<WfProcNodeVo> buildTaskNodeList(
            Collection<UserTask> taskDefinitions,
            List<HistoricTaskInstance> historicTasks,
            List<Task> activeTasks,
            List<Comment> allComments,
            boolean isSubProcessCompleted) {

        List<WfProcNodeVo> nodeVoList = new ArrayList<>();

        if (isSubProcessCompleted) {
            // --- 场景1: 子流程已完成，只展示实际发生过的历史任务 ---
            for (HistoricTaskInstance histTask : historicTasks) {
                WfProcNodeVo nodeVo = new WfProcNodeVo();
                nodeVo.setActivityId(histTask.getTaskDefinitionKey());
                nodeVo.setActivityName(histTask.getName());
                nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
                nodeVo.setIsActive(false); // 已完成的实例里不可能有活动任务
                nodeVo.setCreateTime(histTask.getCreateTime());
                nodeVo.setEndTime(histTask.getEndTime());
                nodeVo.setAssigneeId(histTask.getAssignee());
                nodeVo.setAssigneeName(adapter.getNickNameById(histTask.getAssignee()));

                // 关联评论
                List<FlowComment> comments = allComments.stream()
                        .filter(c -> histTask.getId().equals(c.getTaskId()))
                        .map(this::convertCommentToFlowComment) // 假设您有此转换方法
                        .collect(Collectors.toList());
                nodeVo.setCommentList(comments);

                nodeVoList.add(nodeVo);
            }
        } else {
            // --- 场景2: 子流程进行中，展示所有定义的任务及其状态 ---
            Map<String, List<HistoricTaskInstance>> historicTaskMap = historicTasks.stream()
                    .collect(Collectors.groupingBy(HistoricTaskInstance::getTaskDefinitionKey));
            Map<String, List<Task>> activeTaskMap = activeTasks.stream()
                    .collect(Collectors.groupingBy(Task::getTaskDefinitionKey));
            // 遍历所有任务定义，以构建完整的节点列表，确保未开始的任务也能展示
            for (UserTask taskDef : taskDefinitions) {
                String activityId = taskDef.getId();
                List<HistoricTaskInstance> nodeHistoricTasks = historicTaskMap.getOrDefault(activityId, Collections.emptyList());
                List<Task> nodeActiveTasks = activeTaskMap.getOrDefault(activityId, Collections.emptyList());

                WfProcNodeVo nodeVo = new WfProcNodeVo();
                nodeVo.setActivityId(activityId);
                nodeVo.setActivityName(taskDef.getName());
                nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);

                if (!nodeActiveTasks.isEmpty()) {
                    // 状态：进行中
                    nodeVo.setIsActive(true);
                    nodeVo.setCreateTime(nodeActiveTasks.stream().map(Task::getCreateTime).min(Date::compareTo).orElse(null));
                    String taskIds = nodeActiveTasks.stream().map(Task::getId).collect(Collectors.joining(","));
                    String assignees = nodeActiveTasks.stream()
                            .map(t -> StringUtils.isNotBlank(t.getAssignee()) ? adapter.getNickNameById(t.getAssignee()) : getCandidateInfoForActiveTask(t))
                            .distinct().collect(Collectors.joining("; "));
//                nodeVo.setTaskId(taskIds);
                    nodeVo.setAssigneeName(assignees);
                    
                    // 当前活动任务的评论列表置空
                    nodeVo.setCommentList(new ArrayList<>());

                } else if (!nodeHistoricTasks.isEmpty()) {
                    // 状态：已完成
                    nodeVo.setIsActive(false);
                    nodeVo.setCreateTime(nodeHistoricTasks.stream().map(HistoricTaskInstance::getCreateTime).min(Date::compareTo).orElse(null));
                    nodeVo.setEndTime(nodeHistoricTasks.stream().map(HistoricTaskInstance::getEndTime).max(Date::compareTo).orElse(null));

                    String assignees = nodeHistoricTasks.stream()
                            .map(t -> adapter.getNickNameById(t.getAssignee())).distinct().collect(Collectors.joining(","));
                    nodeVo.setAssigneeName(assignees);

                    // 聚合评论 - 只保留最新的一条评论
                    List<String> taskIds = nodeHistoricTasks.stream().map(HistoricTaskInstance::getId).collect(Collectors.toList());
                    List<FlowComment> comments = allComments.stream()
                            .filter(c -> taskIds.contains(c.getTaskId()))
                            .map(this::convertCommentToFlowComment)
                            .sorted(Comparator.comparing(FlowComment::getTime, Comparator.nullsLast(Comparator.reverseOrder())))
                            .limit(1) // 只保留最新的一条评论
                            .collect(Collectors.toList());
                    nodeVo.setCommentList(comments);

                } else {
                    // 状态：未开始
                    nodeVo.setIsActive(false);
                    nodeVo.setAssigneeName("（待处理）"); // 或显示候选人信息
                    // nodeVo.setCandidate(getCandidateInfo(taskDef));
                    
                    // 未开始任务的评论列表置空，并且开始时间和结束时间也置空
                    nodeVo.setCommentList(new ArrayList<>());
                    nodeVo.setCreateTime(null);
                    nodeVo.setEndTime(null);
                }
                nodeVoList.add(nodeVo);
            }
        }


        // 根据创建时间对节点排序，保证展示顺序的逻辑性
        nodeVoList.sort(Comparator.comparing(WfProcNodeVo::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())));
        return nodeVoList;
    }

    /**
     * 获取指定用户节点的最新审批记录
     * 优化版本：支持多实例节点的同一次触发批次识别，只查询历史记录
     *
     * @param procInstId 流程实例ID
     * @param nodeIds 用户节点ID列表，多个用逗号分隔。如果为空，使用默认节点
     * @return 最新审批记录列表
     */
    @Override
    public List<WfApprovalRecordVo> getLatestApprovalRecords(String procInstId, String nodeIds) {
        if (StrUtil.isBlank(procInstId)) {
            throw new RestException("流程实例ID不能为空");
        }

        // 如果没有传入节点ID，使用默认节点：校办副主任、领导审批、联络人处理
        String targetNodeIds = nodeIds;
        if (StrUtil.isBlank(targetNodeIds)) {
            targetNodeIds = "Activity_deputy_director,Activity_school_leader,Activity_dept_liaison";
            log.info("未指定节点ID，使用默认节点: {}", targetNodeIds);
        }

        // 解析节点ID列表
        List<String> nodeIdList = Arrays.asList(targetNodeIds.split(","))
                .stream()
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (nodeIdList.isEmpty()) {
            throw new RestException("节点ID列表不能为空");
        }

        List<WfApprovalRecordVo> approvalRecords = new ArrayList<>();

        try {
            // 为每个节点查询最新的审批记录（支持多实例）
            for (String nodeId : nodeIdList) {
                List<WfApprovalRecordVo> latestRecords = getLatestApprovalRecordsForNode(procInstId, nodeId);
                if (CollUtil.isNotEmpty(latestRecords)) {
                    approvalRecords.addAll(latestRecords);
                }
            }

            // 按完成时间倒序排列
            approvalRecords.sort((a, b) -> {
                Date timeA = a.getEndTime() != null ? a.getEndTime() : a.getStartTime();
                Date timeB = b.getEndTime() != null ? b.getEndTime() : b.getStartTime();
                if (timeA == null && timeB == null) return 0;
                if (timeA == null) return 1;
                if (timeB == null) return -1;
                return timeB.compareTo(timeA);
            });

        } catch (Exception e) {
            log.error("获取审批记录失败，流程实例ID: {}, 节点ID: {}", procInstId, nodeIds, e);
            throw new RestException("获取审批记录失败: " + e.getMessage());
        }

        return approvalRecords;
    }

    /**
     * 获取指定节点的最新审批记录（支持多实例）
     * 优化版本：
     * 1. 如果是多实例节点，返回同一次触发的所有实例的审批记录
     * 2. 只查询历史记录，不查询活动任务
     * 3. 通过时间窗口识别同一批次的多实例
     *
     * @param procInstId 流程实例ID
     * @param nodeId 节点ID
     * @return 最新批次的审批记录列表，如果没有则返回空列表
     */
    private List<WfApprovalRecordVo> getLatestApprovalRecordsForNode(String procInstId, String nodeId) {
        // 查询该节点的所有历史任务实例，按开始时间倒序
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(procInstId)
                .taskDefinitionKey(nodeId)
                .orderByHistoricTaskInstanceStartTime().desc()
                .list();

        if (CollUtil.isEmpty(historicTasks)) {
            log.debug("节点 {} 在流程实例 {} 中没有历史任务记录", nodeId, procInstId);
            return new ArrayList<>();
        }

        // 获取最新任务的开始时间作为基准
        HistoricTaskInstance latestTask = historicTasks.get(0);
        Date latestStartTime = latestTask.getStartTime();

        if (latestStartTime == null) {
            log.warn("最新任务的开始时间为空，节点: {}, 流程实例: {}", nodeId, procInstId);
            return Collections.singletonList(convertHistoricTaskToApprovalRecord(latestTask));
        }

        // 查找同一批次的所有任务（在时间窗口内开始的任务）
        List<HistoricTaskInstance> sameBatchTasks = new ArrayList<>();
        for (HistoricTaskInstance task : historicTasks) {
            Date taskStartTime = task.getStartTime();
            if (taskStartTime != null) {
                long timeDiff = Math.abs(latestStartTime.getTime() - taskStartTime.getTime());
                if (timeDiff <= MULTI_INSTANCE_BATCH_TIME_WINDOW) {
                    sameBatchTasks.add(task);
                } else {
                    // 由于按时间倒序排列，一旦超出时间窗口就可以停止
                    break;
                }
            }
        }

        log.debug("节点 {} 在流程实例 {} 中找到同一批次的任务数量: {}", nodeId, procInstId, sameBatchTasks.size());

        // 转换为审批记录列表
        List<WfApprovalRecordVo> approvalRecords = new ArrayList<>();
        for (HistoricTaskInstance task : sameBatchTasks) {
            WfApprovalRecordVo record = convertHistoricTaskToApprovalRecord(task);
            if (record != null) {
                approvalRecords.add(record);
            }
        }

        return approvalRecords;
    }

    /**
     * 将历史任务实例转换为审批记录VO
     *
     * @param historicTask 历史任务实例
     * @return 审批记录VO
     */
    private WfApprovalRecordVo convertHistoricTaskToApprovalRecord(HistoricTaskInstance historicTask) {
        WfApprovalRecordVo record = new WfApprovalRecordVo();

        record.setTaskId(historicTask.getId());
        record.setTaskName(historicTask.getName());
        record.setTaskDefKey(historicTask.getTaskDefinitionKey());
        record.setProcessInstanceId(historicTask.getProcessInstanceId());
        record.setProcessDefinitionId(historicTask.getProcessDefinitionId());
        record.setAssignee(historicTask.getAssignee());
        record.setStartTime(historicTask.getStartTime());
        record.setEndTime(historicTask.getEndTime());
        record.setDurationInMillis(historicTask.getDurationInMillis());
        record.setPriority(historicTask.getPriority());
        record.setDueDate(historicTask.getDueDate());
        record.setClaimTime(historicTask.getClaimTime());
        record.setDeleteReason(historicTask.getDeleteReason());
        record.setIsLatest(true);
        record.setStatus("已完成");

        // 格式化持续时间
        if (historicTask.getDurationInMillis() != null) {
            record.setDuration(DateUtil.formatBetween(historicTask.getDurationInMillis(), BetweenFormatter.Level.SECOND));
        }

        // 获取用户信息和评论
        enrichApprovalRecordWithUserInfo(record);
        enrichApprovalRecordWithComment(record);

        return record;
    }

    /**
     * 将活动任务转换为审批记录VO
     *
     * @param activeTask 活动任务
     * @return 审批记录VO
     */
    private WfApprovalRecordVo convertActiveTaskToApprovalRecord(Task activeTask) {
        WfApprovalRecordVo record = new WfApprovalRecordVo();

        record.setTaskId(activeTask.getId());
        record.setTaskName(activeTask.getName());
        record.setTaskDefKey(activeTask.getTaskDefinitionKey());
        record.setProcessInstanceId(activeTask.getProcessInstanceId());
        record.setProcessDefinitionId(activeTask.getProcessDefinitionId());
        record.setAssignee(activeTask.getAssignee());
        record.setStartTime(activeTask.getCreateTime());
        record.setEndTime(null); // 活动任务未完成
        record.setDurationInMillis(null);
        record.setPriority(activeTask.getPriority());
        record.setDueDate(activeTask.getDueDate());
        record.setClaimTime(activeTask.getClaimTime());
        record.setDeleteReason(null);
        record.setIsLatest(true);
        record.setStatus("进行中");

        // 获取用户信息
        enrichApprovalRecordWithUserInfo(record);

        return record;
    }

    /**
     * 丰富审批记录的用户信息
     *
     * @param record 审批记录
     */
    private void enrichApprovalRecordWithUserInfo(WfApprovalRecordVo record) {
        if (StrUtil.isNotBlank(record.getAssignee())) {
            try {
                // 通过适配器获取用户基本信息
                String userName = adapter.getUserNameById(record.getAssignee());
                String nickName = adapter.getNickNameById(record.getAssignee());

                // 构建CCDto对象
                CCDto userInfo = new CCDto();
                userInfo.setUsername(record.getAssignee());
                userInfo.setDisplayname(StrUtil.isNotBlank(userName) ? userName : nickName);

                // 设置用户信息
                record.setAssigneeInfo(userInfo);
                record.setAssigneeName(userInfo.getDisplayname());

                // 尝试获取部门信息（这里可能需要根据实际业务逻辑调整）
                // 由于BizSystemAdapter没有直接通过用户ID获取部门信息的方法，
                // 这里暂时不设置部门信息，或者可以通过其他方式获取

            } catch (Exception e) {
                log.warn("获取用户信息失败，用户ID: {}", record.getAssignee(), e);
                // 设置默认值
                record.setAssigneeName(record.getAssignee());
            }
        }
    }

    /**
     * 丰富审批记录的评论信息
     *
     * @param record 审批记录
     */
    private void enrichApprovalRecordWithComment(WfApprovalRecordVo record) {
        if (StrUtil.isNotBlank(record.getTaskId())) {
            try {
                // 获取任务相关的评论
                List<Comment> comments = taskService.getTaskComments(record.getTaskId(), "1");
                if (CollUtil.isNotEmpty(comments)) {
                    // 获取最新的评论
                    Comment latestComment = comments.stream()
                            .filter(comment -> StrUtil.isNotBlank(comment.getFullMessage()))
                            .max(Comparator.comparing(Comment::getTime))
                            .orElse(null);

                    if (latestComment != null) {
                        WfCommentVo wfCommentVo = new WfCommentVo().setType(latestComment.getType())
                            .setTime(latestComment.getTime()).setMessage(latestComment.getFullMessage());
                        record.setComment(Collections.singletonList(wfCommentVo));
                    }
                }
            } catch (Exception e) {
                log.warn("获取任务评论失败，任务ID: {}", record.getTaskId(), e);
            }
        }
    }

    /**
     * 合并并行多实例任务（改进版）
     * 区分真正的并行多实例任务和驳回重审导致的重复节点记录
     *
     * 算法逻辑：
     * 1. 按taskDefinitionKey分组
     * 2. 在每个分组内按审批轮次识别（基于时间间隔）
     * 3. 每个轮次内的并行任务进行合并，不同轮次分别展示
     *
     * @param tasks 原始任务列表
     * @return 合并后的任务列表
     */
    private List<HistoricTaskInstance> mergeParallelMultiInstanceTasks(List<HistoricTaskInstance> tasks) {
        if (CollUtil.isEmpty(tasks)) {
            return tasks;
        }

        // 按taskDefinitionKey分组
        Map<String, List<HistoricTaskInstance>> taskGroups = tasks.stream()
                .collect(Collectors.groupingBy(HistoricTaskInstance::getTaskDefinitionKey));

        List<HistoricTaskInstance> mergedTasks = new ArrayList<>();

        for (Map.Entry<String, List<HistoricTaskInstance>> entry : taskGroups.entrySet()) {
            List<HistoricTaskInstance> groupTasks = entry.getValue();

            if (groupTasks.size() == 1) {
                // 单实例任务，直接添加
                mergedTasks.add(groupTasks.get(0));
            } else {
                // 多实例任务，按审批轮次分组处理
                List<List<HistoricTaskInstance>> approvalRounds = identifyApprovalRounds(groupTasks);

                for (int roundIndex = 0; roundIndex < approvalRounds.size(); roundIndex++) {
                    List<HistoricTaskInstance> roundTasks = approvalRounds.get(roundIndex);
                    int roundNumber = roundIndex + 1; // 轮次从1开始
                    boolean isReapproval = roundIndex > 0; // 第二轮及以后为重新审批

                    if (roundTasks.size() == 1) {
                        // 单个任务，设置轮次信息后添加
                        HistoricTaskInstance task = roundTasks.get(0);
                        HistoricTaskInstance wrappedTask = createRoundInfoTask(task, roundNumber, isReapproval);
                        mergedTasks.add(wrappedTask);
                    } else {
                        // 检查轮次内是否为并行多实例
                        List<HistoricTaskInstance> parallelTasks = identifyParallelMultiInstance(roundTasks);
                        if (!parallelTasks.isEmpty() && parallelTasks.size() > 1) {
                            // 创建合并后的代表性任务，包含轮次信息
                            HistoricTaskInstance mergedTask = createMergedTaskWithRoundInfo(parallelTasks, roundNumber, isReapproval);
                            mergedTasks.add(mergedTask);

                            // 如果还有其他非并行的任务，也要添加
                            List<HistoricTaskInstance> remainingTasks = new ArrayList<>(roundTasks);
                            remainingTasks.removeAll(parallelTasks);
                            for (HistoricTaskInstance task : remainingTasks) {
                                HistoricTaskInstance wrappedTask = createRoundInfoTask(task, roundNumber, isReapproval);
                                mergedTasks.add(wrappedTask);
                            }
                        } else {
                            // 不是并行多实例，设置轮次信息后添加所有任务
                            for (HistoricTaskInstance task : roundTasks) {
                                HistoricTaskInstance wrappedTask = createRoundInfoTask(task, roundNumber, isReapproval);
                                mergedTasks.add(wrappedTask);
                            }
                        }
                    }
                }
            }
        }

        // 按创建时间排序，保持原有顺序
        mergedTasks.sort(Comparator.comparing(HistoricTaskInstance::getCreateTime));

        return mergedTasks;
    }

    /**
     * 识别审批轮次
     * 将同一节点的任务按时间间隔分组，区分正常并行多实例和驳回重审
     *
     * @param tasks 同一节点的任务列表
     * @return 按审批轮次分组的任务列表
     */
    private List<List<HistoricTaskInstance>> identifyApprovalRounds(List<HistoricTaskInstance> tasks) {
        if (tasks.size() <= 1) {
            return Collections.singletonList(tasks);
        }

        // 按创建时间排序
        List<HistoricTaskInstance> sortedTasks = tasks.stream()
                .sorted(Comparator.comparing(HistoricTaskInstance::getCreateTime))
                .collect(Collectors.toList());

        List<List<HistoricTaskInstance>> rounds = new ArrayList<>();
        List<HistoricTaskInstance> currentRound = new ArrayList<>();
        currentRound.add(sortedTasks.get(0));

        for (int i = 1; i < sortedTasks.size(); i++) {
            HistoricTaskInstance currentTask = sortedTasks.get(i);
            HistoricTaskInstance previousTask = sortedTasks.get(i - 1);

            // 计算与前一个任务的时间间隔
            long timeInterval = currentTask.getCreateTime().getTime() - previousTask.getCreateTime().getTime();

            if (timeInterval > APPROVAL_ROUND_INTERVAL_THRESHOLD) {
                // 时间间隔超过阈值，开始新的审批轮次
                rounds.add(new ArrayList<>(currentRound));
                currentRound.clear();
            }
            currentRound.add(currentTask);
        }

        // 添加最后一个轮次
        if (!currentRound.isEmpty()) {
            rounds.add(currentRound);
        }

        return rounds;
    }

    /**
     * 创建带轮次信息的任务包装器
     *
     * @param task 原始任务
     * @param roundNumber 轮次编号
     * @param isReapproval 是否为重新审批
     * @return 包装后的任务
     */
    private HistoricTaskInstance createRoundInfoTask(HistoricTaskInstance task, int roundNumber, boolean isReapproval) {
        return new org.ahead4.workflow.domain.wrapper.RoundInfoTaskInstance(task, roundNumber, isReapproval);
    }

    /**
     * 创建带轮次信息的合并任务
     *
     * @param parallelTasks 并行任务列表
     * @param roundNumber 轮次编号
     * @param isReapproval 是否为重新审批
     * @return 合并后的任务
     */
    private HistoricTaskInstance createMergedTaskWithRoundInfo(List<HistoricTaskInstance> parallelTasks,
                                                               int roundNumber, boolean isReapproval) {
        // 选择代表性任务（通常是第一个完成的任务）
        HistoricTaskInstance representative = parallelTasks.stream()
                .filter(task -> task.getEndTime() != null)
                .min(Comparator.comparing(HistoricTaskInstance::getEndTime))
                .orElse(parallelTasks.get(0));

        return new org.ahead4.workflow.domain.wrapper.MergedHistoricTaskInstance(
                representative, parallelTasks, roundNumber, isReapproval);
    }

    /**
     * 识别并行多实例任务
     * 优化算法：结合时间窗口和上一任务结束时间判断
     *
     * @param tasks 同一活动节点的任务列表
     * @return 识别出的并行多实例任务列表
     */
    private List<HistoricTaskInstance> identifyParallelMultiInstance(List<HistoricTaskInstance> tasks) {
        if (tasks.size() <= 1) {
            return Collections.emptyList();
        }

        // 按创建时间排序
        List<HistoricTaskInstance> sortedTasks = tasks.stream()
                .sorted(Comparator.comparing(HistoricTaskInstance::getCreateTime))
                .collect(Collectors.toList());

        // 获取第一个任务的创建时间作为基准
        Date firstCreateTime = sortedTasks.get(0).getCreateTime();

        // 查找上一个任务的结束时间作为起点
        Date previousTaskEndTime = findPreviousTaskEndTime(sortedTasks.get(0));

        List<HistoricTaskInstance> parallelTasks = new ArrayList<>();

        for (HistoricTaskInstance task : sortedTasks) {
            Date taskCreateTime = task.getCreateTime();

            // 条件1：在时间窗口内
            long timeDiffFromFirst = Math.abs(taskCreateTime.getTime() - firstCreateTime.getTime());
            boolean withinTimeWindow = timeDiffFromFirst <= MULTI_INSTANCE_BATCH_TIME_WINDOW;

            // 条件2：在上一任务结束时间之后（如果存在上一任务）
            boolean afterPreviousTask = previousTaskEndTime == null ||
                    taskCreateTime.getTime() >= previousTaskEndTime.getTime();

            if (withinTimeWindow && afterPreviousTask) {
                parallelTasks.add(task);
            }
        }

        // 如果大部分任务都满足条件，认为是并行多实例
        double parallelRatio = (double) parallelTasks.size() / tasks.size();
        return parallelRatio >= 0.8 ? parallelTasks : Collections.emptyList();
    }

    /**
     * 查找上一个任务的结束时间
     * 用于防止短时间内快速审批导致的时间窗口判断错误
     *
     * @param currentTask 当前任务
     * @return 上一个任务的结束时间，如果没有则返回null
     */
    private Date findPreviousTaskEndTime(HistoricTaskInstance currentTask) {
        try {
            // 查询同一流程实例中在当前任务之前结束的任务
            List<HistoricTaskInstance> previousTasks = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(currentTask.getProcessInstanceId())
                    .finished()
                    .taskCreatedBefore(currentTask.getCreateTime())
                    .orderByHistoricTaskInstanceEndTime()
                    .desc()
                    .list();

            if (CollUtil.isNotEmpty(previousTasks)) {
                return previousTasks.get(0).getEndTime();
            }
        } catch (Exception e) {
            log.warn("查找上一任务结束时间失败，流程实例: {}, 任务: {}",
                    currentTask.getProcessInstanceId(), currentTask.getId(), e);
        }

        return null;
    }

    /**
     * 创建合并后的代表性任务
     *
     * @param parallelTasks 并行多实例任务列表
     * @return 合并后的任务实例
     */
    private HistoricTaskInstance createMergedTask(List<HistoricTaskInstance> parallelTasks) {
        // 选择最新完成的任务作为代表，如果都未完成则选择最新创建的
        HistoricTaskInstance representative = parallelTasks.stream()
                .filter(task -> task.getEndTime() != null)
                .max(Comparator.comparing(HistoricTaskInstance::getEndTime))
                .orElse(parallelTasks.stream()
                        .max(Comparator.comparing(HistoricTaskInstance::getCreateTime))
                        .orElse(parallelTasks.get(0)));

        return new MergedHistoricTaskInstance(representative, parallelTasks);
    }
}
