package org.ahead4.workflow.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WfProcessHistoryVo {
    private String activityId;
    private String activityName;
    private String assignee;
    private String assigneeName;
    private Date startTime;
    private Date endTime;
    private WfCommentVo comment;
    private boolean isSubProcess;
    private String subProcessInstanceId;
    private Object subProcessDetails;

    // 多实例相关字段
    private boolean isMultiInstance = false;           // 是否为多实例任务
    private int multiInstanceCount = 1;                // 多实例数量，默认为1
    private List<String> multiInstanceAssignees;       // 多实例处理人列表
    private List<WfCommentVo> multiInstanceComments;   // 多实例评论列表
    private Date earliestStartTime;                     // 最早开始时间
    private Date latestEndTime;                        // 最晚结束时间
}