package org.ahead4.workflow.domain.wrapper;

import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Task instance wrapper with approval round information
 * Used to add round information to single tasks
 *
 * <AUTHOR>
 * @createTime 2024/01/01
 */
public class RoundInfoTaskInstance implements HistoricTaskInstance {

    private final HistoricTaskInstance delegate;        // 委托的原始任务
    private final Integer approvalRound;                // 审批轮次
    private final boolean isReapproval;                 // 是否为重新审批

    public RoundInfoTaskInstance(HistoricTaskInstance delegate, Integer approvalRound, boolean isReapproval) {
        this.delegate = delegate;
        this.approvalRound = approvalRound;
        this.isReapproval = isReapproval;
    }

    /**
     * 获取审批轮次
     */
    public Integer getApprovalRound() {
        return approvalRound;
    }

    /**
     * 是否为重新审批
     */
    public boolean isReapproval() {
        return isReapproval;
    }

    /**
     * 获取委托的原始任务
     */
    public HistoricTaskInstance getDelegate() {
        return delegate;
    }

    // 所有方法委托给原始任务
    @Override
    public String getId() {
        return delegate.getId();
    }

    @Override
    public String getName() {
        return delegate.getName();
    }

    @Override
    public String getDescription() {
        return delegate.getDescription();
    }

    @Override
    public int getPriority() {
        return delegate.getPriority();
    }

    @Override
    public String getOwner() {
        return delegate.getOwner();
    }

    @Override
    public String getAssignee() {
        return delegate.getAssignee();
    }

    @Override
    public String getTaskDefinitionKey() {
        return delegate.getTaskDefinitionKey();
    }

    @Override
    public String getFormKey() {
        return delegate.getFormKey();
    }

    @Override
    public String getProcessInstanceId() {
        return delegate.getProcessInstanceId();
    }

    @Override
    public String getExecutionId() {
        return delegate.getExecutionId();
    }

    @Override
    public String getProcessDefinitionId() {
        return delegate.getProcessDefinitionId();
    }

    @Override
    public Date getCreateTime() {
        return delegate.getCreateTime();
    }

    @Override
    public Date getStartTime() {
        return delegate.getStartTime();
    }

    @Override
    public Date getEndTime() {
        return delegate.getEndTime();
    }

    @Override
    public Long getDurationInMillis() {
        return delegate.getDurationInMillis();
    }

    @Override
    public Long getWorkTimeInMillis() {
        return delegate.getWorkTimeInMillis();
    }

    @Override
    public String getDeleteReason() {
        return delegate.getDeleteReason();
    }

    @Override
    public String getParentTaskId() {
        return delegate.getParentTaskId();
    }

    @Override
    public Date getDueDate() {
        return delegate.getDueDate();
    }

    @Override
    public String getCategory() {
        return delegate.getCategory();
    }

    @Override
    public String getTenantId() {
        return delegate.getTenantId();
    }

    @Override
    public Date getClaimTime() {
        return delegate.getClaimTime();
    }

    @Override
    public String getTaskDefinitionId() {
        return delegate.getTaskDefinitionId();
    }

    @Override
    public String getScopeId() {
        return delegate.getScopeId();
    }

    @Override
    public String getSubScopeId() {
        return delegate.getSubScopeId();
    }

    @Override
    public String getScopeType() {
        return delegate.getScopeType();
    }

    @Override
    public String getScopeDefinitionId() {
        return delegate.getScopeDefinitionId();
    }

    @Override
    public String getPropagatedStageInstanceId() {
        return delegate.getPropagatedStageInstanceId();
    }

    @Override
    public String getState() {
        return delegate.getState();
    }

    @Override
    public List<IdentityLinkInfo> getIdentityLinks() {
        return delegate.getIdentityLinks();
    }

    @Override
    public Map<String, Object> getProcessVariables() {
        return delegate.getProcessVariables();
    }

    @Override
    public Map<String, Object> getTaskLocalVariables() {
        return delegate.getTaskLocalVariables();
    }

    @Override
    public Date getTime() {
        return delegate.getTime();
    }

    @Override
    public String getLogNumber() {
        return delegate.getLogNumber();
    }

    @Override
    public String getType() {
        return delegate.getType();
    }

    @Override
    public String getUserId() {
        return delegate.getUserId();
    }

    @Override
    public String getData() {
        return delegate.getData();
    }
}
