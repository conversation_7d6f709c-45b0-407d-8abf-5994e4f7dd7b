package org.ahead4.workflow.domain.wrapper;

import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Merged historic task instance wrapper class
 * Used to represent merged results of parallel multi-instance tasks
 *
 * <AUTHOR>
 * @createTime 2024/01/01
 */
public class MergedHistoricTaskInstance implements HistoricTaskInstance {

    private final HistoricTaskInstance representative;  // Representative task
    private final List<HistoricTaskInstance> allTasks;  // All parallel tasks
    private final Integer approvalRound;                // 审批轮次
    private final boolean isReapproval;                 // 是否为重新审批

    public MergedHistoricTaskInstance(HistoricTaskInstance representative, List<HistoricTaskInstance> allTasks) {
        this(representative, allTasks, null, false);
    }

    public MergedHistoricTaskInstance(HistoricTaskInstance representative, List<HistoricTaskInstance> allTasks,
                                      Integer approvalRound, boolean isReapproval) {
        this.representative = representative;
        this.allTasks = allTasks;
        this.approvalRound = approvalRound;
        this.isReapproval = isReapproval;
    }

    /**
     * 获取所有并行任务
     */
    public List<HistoricTaskInstance> getAllTasks() {
        return allTasks;
    }

    /**
     * Get representative task
     */
    public HistoricTaskInstance getRepresentative() {
        return representative;
    }

    /**
     * Whether it is a merged task
     */
    public boolean isMerged() {
        return allTasks.size() > 1;
    }

    // Methods delegated to representative task - only implement methods that exist in Flowable 6.8.1
    @Override
    public String getId() {
        return representative.getId();
    }

    @Override
    public String getProcessDefinitionId() {
        return representative.getProcessDefinitionId();
    }

    @Override
    public String getProcessInstanceId() {
        return representative.getProcessInstanceId();
    }

    @Override
    public String getExecutionId() {
        return representative.getExecutionId();
    }

    @Override
    public String getName() {
        return representative.getName();
    }

    @Override
    public String getDescription() {
        return representative.getDescription();
    }

    @Override
    public String getDeleteReason() {
        return representative.getDeleteReason();
    }

    @Override
    public String getOwner() {
        return representative.getOwner();
    }

    @Override
    public String getAssignee() {
        return representative.getAssignee();
    }

    @Override
    public Date getStartTime() {
        return representative.getStartTime();
    }

    @Override
    public Date getEndTime() {
        return representative.getEndTime();
    }

    @Override
    public Long getDurationInMillis() {
        return representative.getDurationInMillis();
    }

    @Override
    public Long getWorkTimeInMillis() {
        return representative.getWorkTimeInMillis();
    }

    @Override
    public Date getCreateTime() {
        return representative.getCreateTime();
    }

    @Override
    public String getTaskDefinitionKey() {
        return representative.getTaskDefinitionKey();
    }

    @Override
    public String getFormKey() {
        return representative.getFormKey();
    }

    @Override
    public int getPriority() {
        return representative.getPriority();
    }

    @Override
    public Date getDueDate() {
        return representative.getDueDate();
    }

    @Override
    public String getParentTaskId() {
        return representative.getParentTaskId();
    }

    @Override
    public String getTaskDefinitionId() {
        return representative.getTaskDefinitionId();
    }

    @Override
    public Date getClaimTime() {
        return representative.getClaimTime();
    }

    @Override
    public String getTenantId() {
        return representative.getTenantId();
    }

    @Override
    public String getCategory() {
        return representative.getCategory();
    }

    @Override
    public Map<String, Object> getProcessVariables() {
        return representative.getProcessVariables();
    }

    @Override
    public List<? extends IdentityLinkInfo> getIdentityLinks() {
        return representative.getIdentityLinks();
    }

    @Override
    public Map<String, Object> getCaseVariables() {
        return representative.getCaseVariables();
    }

    @Override
    public Map<String, Object> getTaskLocalVariables() {
        return representative.getTaskLocalVariables();
    }

    // Additional TaskInfo interface methods that may be required
    @Override
    public String getScopeId() {
        return representative.getScopeId();
    }

    @Override
    public String getSubScopeId() {
        return representative.getSubScopeId();
    }

    @Override
    public String getScopeType() {
        return representative.getScopeType();
    }

    @Override
    public String getScopeDefinitionId() {
        return representative.getScopeDefinitionId();
    }

    @Override
    public String getPropagatedStageInstanceId() {
        return representative.getPropagatedStageInstanceId();
    }

    // HistoricData interface method
    @Override
    public Date getTime() {
        return representative.getTime();
    }

}
