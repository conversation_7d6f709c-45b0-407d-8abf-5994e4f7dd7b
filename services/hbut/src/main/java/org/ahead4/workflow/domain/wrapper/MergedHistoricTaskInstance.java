package org.ahead4.workflow.domain.wrapper;

import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.Date;
import java.util.List;

/**
 * 合并的历史任务实例包装类
 * 用于表示并行多实例任务的合并结果
 * 
 * <AUTHOR>
 * @createTime 2024/01/01
 */
public class MergedHistoricTaskInstance implements HistoricTaskInstance {
    
    private final HistoricTaskInstance representative;  // 代表性任务
    private final List<HistoricTaskInstance> allTasks;  // 所有并行任务
    
    public MergedHistoricTaskInstance(HistoricTaskInstance representative, List<HistoricTaskInstance> allTasks) {
        this.representative = representative;
        this.allTasks = allTasks;
    }
    
    /**
     * 获取所有并行任务
     */
    public List<HistoricTaskInstance> getAllTasks() {
        return allTasks;
    }
    
    /**
     * 获取代表性任务
     */
    public HistoricTaskInstance getRepresentative() {
        return representative;
    }
    
    /**
     * 是否为合并任务
     */
    public boolean isMerged() {
        return allTasks.size() > 1;
    }
    
    // 委托给代表性任务的方法
    @Override
    public String getId() {
        return representative.getId();
    }
    
    @Override
    public String getProcessDefinitionId() {
        return representative.getProcessDefinitionId();
    }
    
    @Override
    public String getProcessInstanceId() {
        return representative.getProcessInstanceId();
    }
    
    @Override
    public String getExecutionId() {
        return representative.getExecutionId();
    }
    
    @Override
    public String getName() {
        return representative.getName();
    }
    
    @Override
    public String getDescription() {
        return representative.getDescription();
    }
    
    @Override
    public String getDeleteReason() {
        return representative.getDeleteReason();
    }
    
    @Override
    public String getOwner() {
        return representative.getOwner();
    }
    
    @Override
    public String getAssignee() {
        return representative.getAssignee();
    }
    
    @Override
    public Date getStartTime() {
        return representative.getStartTime();
    }
    
    @Override
    public Date getEndTime() {
        return representative.getEndTime();
    }
    
    @Override
    public Long getDurationInMillis() {
        return representative.getDurationInMillis();
    }
    
    @Override
    public Date getCreateTime() {
        return representative.getCreateTime();
    }
    
    @Override
    public String getTaskDefinitionKey() {
        return representative.getTaskDefinitionKey();
    }
    
    @Override
    public String getFormKey() {
        return representative.getFormKey();
    }
    
    @Override
    public int getPriority() {
        return representative.getPriority();
    }
    
    @Override
    public Date getDueDate() {
        return representative.getDueDate();
    }
    
    @Override
    public String getParentTaskId() {
        return representative.getParentTaskId();
    }
    
    @Override
    public String getUrl() {
        return representative.getUrl();
    }
    
    @Override
    public String getTaskDefinitionId() {
        return representative.getTaskDefinitionId();
    }
    
    @Override
    public String getProcessDefinitionKey() {
        return representative.getProcessDefinitionKey();
    }
    
    @Override
    public String getProcessDefinitionName() {
        return representative.getProcessDefinitionName();
    }
    
    @Override
    public Integer getProcessDefinitionVersion() {
        return representative.getProcessDefinitionVersion();
    }
    
    @Override
    public String getDeploymentId() {
        return representative.getDeploymentId();
    }
    
    @Override
    public Date getClaimTime() {
        return representative.getClaimTime();
    }
    
    @Override
    public String getTenantId() {
        return representative.getTenantId();
    }
    
    @Override
    public String getCategory() {
        return representative.getCategory();
    }
    
    @Override
    public Date getLastUpdateTime() {
        return representative.getLastUpdateTime();
    }
}
