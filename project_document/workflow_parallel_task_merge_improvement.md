# 并行多实例任务合并功能改进文档

## 📋 **改进背景**

### 问题描述
原有的并行多实例任务合并逻辑存在关键缺陷：
- **过度合并**：将驳回重审导致的重复节点记录也进行了合并
- **信息丢失**：丢失了重要的审批历史轨迹
- **业务逻辑不完整**：无法区分真正的并行多实例和驳回重审场景

### 用户反馈
> "我检查了合并后的结果，感觉有些不尽如人意，尤其是如果任务在某个节点被驳回，那这个流程会重复经过某些节点，这会导致有些节点有多条记录，像这种记录我们应该要区分出来并展示的，但是现在的逻辑好像直接丢弃了"

## 🎯 **改进目标**

1. **正确区分**：区分真正的并行多实例任务和驳回重审导致的重复节点
2. **保留历史**：确保驳回重审的审批历史完整保留
3. **智能合并**：只合并真正的并行多实例任务
4. **向后兼容**：不影响现有功能的正常使用

## 🔧 **技术实现**

### 1. 新增配置常量

```java
/**
 * 审批轮次间隔阈值（毫秒）- 1小时
 * 超过此时间间隔的同一节点任务被认为是不同的审批轮次（如驳回重审）
 */
private static final long APPROVAL_ROUND_INTERVAL_THRESHOLD = 60 * 60 * 1000L;
```

### 2. 审批轮次识别算法

```java
/**
 * 识别审批轮次
 * 将同一节点的任务按时间间隔分组，区分正常并行多实例和驳回重审
 */
private List<List<HistoricTaskInstance>> identifyApprovalRounds(List<HistoricTaskInstance> tasks) {
    // 按创建时间排序
    // 计算相邻任务的时间间隔
    // 超过阈值则认为是新的审批轮次
}
```

### 3. 数据结构增强

#### WfProcessHistoryVo 新增字段
```java
// 审批轮次相关字段
private Integer approvalRound;                      // 审批轮次（第几轮审批，从1开始）
private boolean isReapproval = false;              // 是否为重新审批（驳回后的重审）
```

#### 新增包装器类
- `RoundInfoTaskInstance`：为单任务添加轮次信息
- 扩展 `MergedHistoricTaskInstance`：支持轮次信息传递

### 4. 合并逻辑重构

```java
// 改进的合并算法流程
1. 按taskDefinitionKey分组
2. 在每个分组内按审批轮次识别（基于时间间隔）
3. 每个轮次内的并行任务进行合并，不同轮次分别展示
4. 为每个任务/合并任务设置轮次信息
```

## 📊 **业务场景支持**

### 场景1：正常并行多实例
- **描述**：同一个节点，同一时间段内，多个人同时处理
- **处理**：合并显示，标记为第1轮审批
- **效果**：减少UI重复记录，提升用户体验

### 场景2：驳回重审
- **描述**：同一个节点，不同时间段，因为驳回后重新流转
- **处理**：分别显示，标记为不同轮次（第1轮、第2轮...）
- **效果**：保留完整审批历史，便于追溯

### 场景3：混合场景
- **描述**：同一节点既有并行多实例，又有驳回重审
- **处理**：按轮次分组，每轮次内的并行任务合并
- **效果**：既减少冗余又保留历史

## 🔍 **关键算法逻辑**

### 时间间隔判断
```java
// 计算与前一个任务的时间间隔
long timeInterval = currentTask.getCreateTime().getTime() - previousTask.getCreateTime().getTime();

if (timeInterval > APPROVAL_ROUND_INTERVAL_THRESHOLD) {
    // 时间间隔超过阈值，开始新的审批轮次
    rounds.add(new ArrayList<>(currentRound));
    currentRound.clear();
}
```

### 轮次内并行识别
```java
// 在每个审批轮次内，应用原有的并行多实例识别逻辑
List<HistoricTaskInstance> parallelTasks = identifyParallelMultiInstance(roundTasks);
if (!parallelTasks.isEmpty() && parallelTasks.size() > 1) {
    // 创建合并后的代表性任务，包含轮次信息
    HistoricTaskInstance mergedTask = createMergedTaskWithRoundInfo(parallelTasks, roundNumber, isReapproval);
}
```

## 📈 **效果对比**

| 方面 | 改进前 | 改进后 |
|------|--------|--------|
| 驳回重审记录 | ❌ 被错误合并 | ✅ 正确保留 |
| 审批历史 | ❌ 信息丢失 | ✅ 完整追溯 |
| 并行任务合并 | ✅ 正常工作 | ✅ 继续工作 |
| 轮次标识 | ❌ 无法区分 | ✅ 清晰标识 |
| 向后兼容 | - | ✅ 完全兼容 |

## 🛠️ **配置参数**

### 可调整参数
- `APPROVAL_ROUND_INTERVAL_THRESHOLD`：审批轮次间隔阈值
  - 默认值：1小时（3600000毫秒）
  - 建议：根据业务实际情况调整
  - 过小：可能将正常的长时间审批误判为新轮次
  - 过大：可能将快速驳回误判为并行审批

## 🧪 **测试建议**

### 测试场景
1. **纯并行多实例**：验证合并功能正常
2. **纯驳回重审**：验证分别显示功能
3. **混合场景**：验证复杂情况处理
4. **边界情况**：测试时间阈值边界

### 验证要点
- 轮次编号正确性
- 重审标识准确性
- 审批历史完整性
- UI展示友好性

## 📝 **后续优化建议**

1. **动态阈值**：根据流程平均处理时间动态调整
2. **驳回标识**：结合流程活动类型进一步优化识别
3. **配置化**：将阈值参数配置化，支持运行时调整
4. **性能优化**：大数据量场景下的性能优化

## ✅ **实施状态**

- [x] 算法设计完成
- [x] 代码实现完成
- [x] 编译验证通过
- [ ] 功能测试验证
- [ ] 生产环境部署

---

**创建时间**：2025-01-06  
**版本**：v1.0  
**状态**：实现完成，待测试验证
